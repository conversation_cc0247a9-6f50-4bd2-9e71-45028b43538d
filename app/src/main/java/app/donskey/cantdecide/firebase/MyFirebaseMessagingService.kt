package app.donskey.cantdecide.firebase

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.media.RingtoneManager
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationManagerCompat
import app.donskey.cantdecide.MainActivity
import app.donskey.cantdecide.data.repositories.UserRepository
import app.donskey.cantdecide.util.SnackbarMessageManager
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import app.donskey.cantdecide.util.NotificationScope
import app.donskey.cantdecide.CantDecideApplication
import app.donskey.cantdecide.R
import javax.inject.Inject

/**
 * Service for handling Firebase Cloud Messaging.
 * This service is responsible for receiving new FCM tokens and handling incoming messages.
 */
@AndroidEntryPoint
class MyFirebaseMessagingService : FirebaseMessagingService() {

    @Inject lateinit var userRepository: UserRepository
    @Inject lateinit var firebaseAuth: FirebaseAuth
    @Inject lateinit var firestore: FirebaseFirestore

    private val job = SupervisorJob()
    // Service-specific scope for tasks tightly bound to the service instance (like saving token in onNewToken)
    private val serviceScope = CoroutineScope(Dispatchers.IO + job)

    companion object {
        private const val TAG = "MyFMService"
        private const val FRIEND_REQUEST_CHANNEL_ID = "friend_requests_channel"
        private const val FRIEND_REQUEST_NOTIFICATION_ID = 1001 // Unique ID for friend request notifications
    }

    /**
     * Called when a new FCM registration token is generated.
     * This token is used to send messages to this specific app instance.
     * We need to save this token to our server (Firestore) so that
     * Cloud Functions can use it to target this device.
     *
     * @param token The new FCM registration token.
     */
    override fun onNewToken(token: String) {
        super.onNewToken(token)
        Log.d(TAG, "Refreshed token: $token")
        // If you need to update this token on your server or for the logged-in user in Firestore:
        serviceScope.launch {
            val userId = userRepository.getCurrentUser()?.uid
            if (userId != null) {
                userRepository.updateFcmToken(userId, token)
            }
        }
    }

    /**
     * Called when a message is received while the app is in the foreground.
     * For messages with a notification payload, this method is called when the app is in the foreground.
     * For messages with a data payload, this method is called regardless of app state (foreground/background).
     *
     * @param remoteMessage Object representing the message received from Firebase Cloud Messaging.
     */
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        Log.d(TAG, "-----------------------------------------------------")
        Log.d(TAG, "NEW MESSAGE RECEIVED in onMessageReceived")
        Log.d(TAG, "From: ${remoteMessage.from}")
        Log.d(TAG, "Message Data: ${remoteMessage.data}")
        if (remoteMessage.notification != null) {
            Log.d(TAG, "Message Notification Title: ${remoteMessage.notification!!.title}")
            Log.d(TAG, "Message Notification Body: ${remoteMessage.notification!!.body}")
        }
        Log.d(TAG, "-----------------------------------------------------")

        // Create notification channel for Android O and above
        createNotificationChannel()

        // Extract data payload
        val data = remoteMessage.data
        val messageType = data["type"]

        // --- Extract notification details from data payload ---
        // Use defaults if specific fields are missing in the data
        val finalTitle = data["title"] ?: "New Friend Request"
        val senderName = data["senderName"] ?: "Someone"
        val finalBody = data["body"] ?: "$senderName sent you a friend request."

        Log.d(TAG, "Message Type: $messageType")
        Log.d(TAG, "Data Title: ${data["title"]}")
        Log.d(TAG, "Data Body: ${data["body"]}")
        Log.d(TAG, "Data SenderName: ${data["senderName"]}")

        // --- Handle Friend Request Message ---
        if (messageType == "friend_request") {
            // Content is already extracted above from the data payload
            val recipientId = data["recipientId"]
            val currentLoggedInUserId = firebaseAuth.currentUser?.uid

            Log.d(TAG, "Friend Request For: $recipientId, Current User: $currentLoggedInUserId")

            // Only process this friend request notification if it's for the current user
            if (currentLoggedInUserId != null && currentLoggedInUserId == recipientId) {
                Log.d(TAG, "Notification is for the current user. Proceeding...")

                // --- Use system notification for both foreground and background (consistent UX) ---
                // We'll check user preferences with a timeout to avoid blocking
                NotificationScope.scope.launch {
                    var shouldNotify = true // Default to true for reliability
                    var userSettingChecked = false

                    try {
                        Log.d(TAG, "Attempting to fetch user profile with timeout...")
                        // Add timeout to prevent hanging
                        val userProfile = withTimeoutOrNull(3000) { // 3 second timeout
                            userRepository.getUserProfile()
                        }

                        Log.d(TAG, "User profile fetch result: ${userProfile?.displayName}")
                        if (userProfile != null) {
                           shouldNotify = userProfile.notificationSettings.notifyOnFriendRequest
                           userSettingChecked = true
                           Log.d(TAG, "User setting checked: shouldNotify = $shouldNotify")
                        } else {
                            Log.w(TAG, "User profile fetch timed out or returned null, using default (true)")
                        }

                    } catch (e: Exception) {
                        Log.e(TAG, "Error fetching user settings, using default (true)", e)
                        // Keep shouldNotify = true (the default)
                    }

                    if (shouldNotify) {
                        Log.d(TAG, "Proceeding with notifications (setting: $shouldNotify, checked: $userSettingChecked)")

                        // Show system notification for both foreground and background
                        // This provides consistent UX regardless of app state
                        try {
                            Log.d(TAG, "Showing system notification...")
                            showFriendRequestNotification(finalTitle, finalBody)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error showing system notification", e)
                        }

                        // Play sound for immediate feedback
                        // The system notification will also play sound via its channel
                        try {
                            Log.d(TAG, "Playing notification sound...")
                            playNotificationSound()
                        } catch (e: Exception) {
                            Log.e(TAG, "Error playing notification sound", e)
                        }

                    } else {
                        Log.d(TAG, "User has disabled friend request notifications. Skipping system notification and sound.")
                    }
                }
            } else {
                Log.w(TAG, "Friend request notification received, but not for the current logged-in user. Recipient: $recipientId, Current: $currentLoggedInUserId. Discarding.")
            }

        } else {
            // --- Handle other message types (non-friend request) ---
            Log.d(TAG, "Received non-friend_request message type: $messageType")
            val otherTitle = data["title"]
            val otherBody = data["body"]
            if (otherTitle != null || otherBody != null) {
                 Log.d(TAG, "Other message data - Title: $otherTitle, Body: $otherBody")
                 // For other messages, we still play sound explicitly as they might not have their own channel sound setup.
                 playNotificationSound()
                 // And potentially show a generic notification or use SnackbarManager
                 // showGenericNotification(otherTitle, otherBody) // Example
            } else {
                 playNotificationSound()
            }
        }
    }

    /**
     * Builds and displays a system notification for a received friend request.
     */
    private suspend fun showFriendRequestNotification(title: String, body: String) = withContext(Dispatchers.Main) {
        // Intent to launch MainActivity when notification is tapped
        val intent = Intent(this@MyFirebaseMessagingService, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            // Optional: Add extra data to tell MainActivity to navigate to Friends screen
            putExtra("navigateTo", "friends")
        }

        // Create PendingIntent
        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            this@MyFirebaseMessagingService,
            0, // Request code
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Build the notification
        val notificationBuilder = NotificationCompat.Builder(this@MyFirebaseMessagingService, FRIEND_REQUEST_CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_stat_notifications) // Pointing to your new, correctly named icon
            .setContentTitle(title)
            .setContentText(body)
            .setStyle(NotificationCompat.BigTextStyle().bigText(body))
            .setPriority(NotificationCompat.PRIORITY_HIGH) // Match channel importance
            .setContentIntent(pendingIntent) // Set the intent that fires when tapped
            .setAutoCancel(true) // Automatically removes the notification when tapped
            // Optional: Add actions, large icon, etc.

        // Show the notification
        try {
            Log.d(TAG, "Attempting to display system notification with NotificationManagerCompat...")
            NotificationManagerCompat.from(this@MyFirebaseMessagingService).notify(FRIEND_REQUEST_NOTIFICATION_ID, notificationBuilder.build())
            Log.d(TAG, "System notification displayed for friend request - IF NO ERRORS ABOVE AND PERMISSIONS ARE OK.")
        } catch (e: SecurityException) {
            // This can happen if the POST_NOTIFICATIONS permission is not granted on Android 13+
            // You should request this permission elsewhere in your app flow.
            Log.e(TAG, "SecurityException: Failed to show notification. Missing POST_NOTIFICATIONS permission?", e)
        } catch (e: Exception) {
            Log.e(TAG, "Error showing system notification", e)
        }
    }

    /**
     * Plays the default notification sound.
     */
    private fun playNotificationSound() {
        try {
            val defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION)
            val r = RingtoneManager.getRingtone(applicationContext, defaultSoundUri)
            r.play()
        } catch (e: Exception) {
            Log.e(TAG, "Error playing notification sound", e)
        }
    }

    /**
     * Creates a notification channel for Android Oreo (API 26) and above.
     * Notifications must be assigned to a channel on newer Android versions.
     */
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val name = "Friend Requests"
            val descriptionText = "Notifications for new friend requests"
            val importance = NotificationManager.IMPORTANCE_HIGH // Or IMPORTANCE_DEFAULT
            val channel = NotificationChannel(FRIEND_REQUEST_CHANNEL_ID, name, importance).apply {
                description = descriptionText
                // Configure other channel properties here if needed (e.g., lights, vibration)
                // enableLights(true)
                // lightColor = Color.RED
                // enableVibration(true)
            }
            // Register the channel with the system
            val notificationManager: NotificationManager =
                getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
            Log.d(TAG, "Notification channel '${FRIEND_REQUEST_CHANNEL_ID}' created.")
        }
    }

    override fun onDeletedMessages() {
        super.onDeletedMessages()
        Log.d(TAG, "Received onDeletedMessages callback. Some messages may have been deleted.")
        // This can happen if messages were pending when the app was offline for too long,
        // or if the app exceeded the GCM server's per-app message limit.
    }

    override fun onDestroy() {
        super.onDestroy()
        // Cancel the job associated with serviceScope
        job.cancel() // Cancel without arguments
    }
}