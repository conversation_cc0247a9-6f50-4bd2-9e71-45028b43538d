package app.donskey.cantdecide.ui.poll_details

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.donskey.cantdecide.data.model.PollVote
import app.donskey.cantdecide.data.model.PrivateTextPoll
import app.donskey.cantdecide.domain.repository.PollsRepository
import app.donskey.cantdecide.domain.repository.UserRepository
import app.donskey.cantdecide.util.Resource
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * UI state for the Poll Details screen.
 *
 * @property isLoading True if data is currently being loaded or an operation is in progress.
 * @property poll The details of the poll being viewed. Null if not yet loaded or if an error occurred.
 * @property userVote The current user's vote on this poll, if they have already voted. Null otherwise.
 * @property selectedOptionId The ID of the poll option that the user has currently selected in the UI (before submitting).
 * @property isSubmittingVote To indicate when a vote submission is in progress.
 * @property error A message describing an error if one has occurred. Null otherwise.
 * @property optionResults List of pre-calculated results for each poll option.
 * @property invitedFriendsVoteStatus List of invited friends' vote status for the poll.
 * @property currentUserId The ID of the current user.
 */
data class PollDetailsUiState(
    val isLoading: Boolean = true,
    val poll: PrivateTextPoll? = null,
    val userVote: PollVote? = null,
    val selectedOptionId: String? = null,
    val isSubmittingVote: Boolean = false,
    val error: String? = null,
    val optionResults: List<OptionResultUiState> = emptyList(),
    val invitedFriendsVoteStatus: List<InvitedFriendVoteStatusViewData> = emptyList(),
    val currentUserId: String? = null
)

/**
 * Data class to hold processed result information for a single poll option.
 */
data class OptionResultUiState(
    val optionId: String,
    val text: String,
    val voteCount: Int,
    val percentage: Float // Represents progress from 0.0f to 1.0f
)

/**
 * One-time side effects for the Poll Details screen.
 */
sealed interface PollDetailsEffect {
    /**
     * Indicates that a vote was successfully submitted by the user.
     */
    data object VoteSubmittedSuccessfully : PollDetailsEffect

    /**
     * Indicates that an error occurred and a message should be shown to the user.
     * @param message The error message to display.
     */
    data class ShowError(val message: String) : PollDetailsEffect

    /**
     * Indicates that the poll has been deleted and the user should be navigated back.
     */
    data object NavigateBackPollDeleted : PollDetailsEffect
}

/**
 * User-triggered events on the Poll Details screen.
 */
sealed interface PollDetailsEvent {
    /**
     * Event triggered when the user selects a poll option.
     * @param optionId The ID of the selected poll option.
     */
    data class OptionSelected(val optionId: String) : PollDetailsEvent

    /**
     * Event triggered when the user clicks the button to submit their vote.
     */
    data object SubmitVoteClicked : PollDetailsEvent
}

/**
 * ViewModel for the Poll Details screen.
 *
 * This ViewModel is responsible for fetching poll details, handling user interactions related to voting,
 * and managing the UI state for the [PollDetailsScreen].
 *
 * @param savedStateHandle Handle to saved state, used here to retrieve the pollId passed via navigation.
 * @param pollsRepository Repository for accessing poll-related data.
 * @param userRepository Repository for accessing current user information.
 */
@HiltViewModel
class PollDetailsViewModel @Inject constructor(
    private val savedStateHandle: SavedStateHandle,
    private val pollsRepository: PollsRepository,
    private val userRepository: UserRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(PollDetailsUiState())
    val uiState: StateFlow<PollDetailsUiState> = _uiState.asStateFlow()

    private val _effect = MutableSharedFlow<PollDetailsEffect>()
    val effect: SharedFlow<PollDetailsEffect> = _effect.asSharedFlow()

    private val pollId: String? = savedStateHandle.get<String>("pollId")

    init {
        val currentUserId = userRepository.getCurrentUser()?.uid
        _uiState.update { it.copy(currentUserId = currentUserId) }

        if (pollId == null) {
            _uiState.update { it.copy(isLoading = false, error = "Poll ID is missing. Cannot load details.") }
        } else {
            // Start observing poll details in real-time.
            observePollDetails(pollId)
            // Initial fetch of user's vote for this poll.
            // This might be re-evaluated if the poll data indicates the user has voted.
            fetchUserVote(pollId)
            // Fetch invited friends vote status if the user is the creator and poll is not anonymous.
            // This will be re-evaluated when poll details are loaded.
        }
    }

    /**
     * Observes real-time updates for the poll specified by [currentPollId].
     * Updates the UI state with the fetched poll details or an error message.
     */
    private fun observePollDetails(currentPollId: String) {
        viewModelScope.launch {
            pollsRepository.getPollDetails(currentPollId)
                .collect { result -> // Collect from the Flow
                    when (result) {
                        is Resource.Success -> {
                            val pollData = result.data
                            // Calculate option results if poll data is available
                            val calculatedOptionResults = pollData?.let { poll ->
                                val totalVotes = poll.voteCount.coerceAtLeast(1) // Avoid division by zero, ensure at least 1 for percentage calculation base if votes exist
                                poll.options.map { option ->
                                    val currentOptionVoteCount = option.voteCount
                                    OptionResultUiState(
                                        optionId = option.optionId,
                                        text = option.text,
                                        voteCount = currentOptionVoteCount,
                                        percentage = if (poll.voteCount > 0) currentOptionVoteCount.toFloat() / totalVotes.toFloat() else 0f
                                    )
                                }
                            } ?: emptyList()

                            _uiState.update {
                                it.copy(
                                    isLoading = false,
                                    poll = pollData,
                                    optionResults = calculatedOptionResults, // Update with calculated results
                                    error = if (pollData == null) "Poll not found or has been deleted." else it.error
                                )
                            }

                            // If poll is deleted/not found, navigate back automatically
                            if (pollData == null) {
                                viewModelScope.launch {
                                    _effect.emit(PollDetailsEffect.NavigateBackPollDeleted)
                                }
                            }
                            // If poll data is successfully loaded, check if user has voted and fetch their specific vote if needed.
                            if (pollData != null) {
                                val userId = _uiState.value.currentUserId
                                if (userId != null && pollData.creatorId == userId && !pollData.anonymous) {
                                    observeInvitedFriendsVoteStatus(currentPollId)
                                }
                                if (userId != null && pollData.votedBy.contains(userId)) {
                                    // User has voted according to the main poll document, ensure we have their specific vote details.
                                    if (_uiState.value.userVote?.pollId != pollData.pollId || _uiState.value.userVote?.voterId != userId) {
                                        fetchUserVote(currentPollId) // Fetch specific vote if not already present or different
                                    }
                                } else {
                                    // User has not voted according to the main poll document, clear any local userVote state.
                                    _uiState.update { it.copy(userVote = null) }
                                }
                            }
                        }
                        is Resource.Error -> {
                            _uiState.update {
                                it.copy(
                                    isLoading = false,
                                    optionResults = emptyList(), // Clear results on error
                                    error = result.message ?: "Failed to load poll details."
                                )
                            }
                        }
                        is Resource.Loading -> {
                            // This state might not be directly emitted by callbackFlow unless explicitly managed.
                            // isLoading is primarily handled by the initial update and Success/Error states.
                            _uiState.update { it.copy(isLoading = true, optionResults = emptyList()) } // Clear results on loading
                        }
                    }
                }
        }
    }

    /**
     * Fetches the current user's vote for the poll specified by [currentPollId].
     * Updates the UI state with the user's vote if found.
     */
    private fun fetchUserVote(currentPollId: String) {
        viewModelScope.launch {
            val userId = _uiState.value.currentUserId ?: return@launch
            when (val result = pollsRepository.getUserVote(currentPollId, userId)) {
                is Resource.Success -> {
                    _uiState.update { it.copy(userVote = result.data) }
                }
                is Resource.Error -> {
                    _uiState.update { it.copy(error = (it.error ?: "") + "\nFailed to check your previous vote: ${result.message}") }
                }
                is Resource.Loading -> {
                }
            }
        }
    }

    /**
     * Handles user-triggered events from the Poll Details screen.
     * @param event The [PollDetailsEvent] to process.
     */
    fun handleEvent(event: PollDetailsEvent) {
        when (event) {
            is PollDetailsEvent.OptionSelected -> {
                if (_uiState.value.userVote == null) {
                    _uiState.update { it.copy(selectedOptionId = event.optionId) }
                }
            }
            PollDetailsEvent.SubmitVoteClicked -> {
                submitCurrentUserVote()
            }
        }
    }

    /**
     * Submits the current user's selected vote to the repository.
     * It checks if an option is selected, if the poll data is available, and if the user is logged in.
     * Emits [PollDetailsEffect.VoteSubmittedSuccessfully] on success or [PollDetailsEffect.ShowError] on failure.
     */
    private fun submitCurrentUserVote() {
        val currentSelectedOptionId = _uiState.value.selectedOptionId
        val currentPoll = _uiState.value.poll
        val currentUser = userRepository.getCurrentUser()

        if (currentSelectedOptionId == null) {
            viewModelScope.launch { _effect.emit(PollDetailsEffect.ShowError("Please select an option to vote.")) }
            return
        }
        if (currentPoll == null) {
            viewModelScope.launch { _effect.emit(PollDetailsEffect.ShowError("Poll details not available. Cannot vote.")) }
            return
        }
        if (currentUser == null) {
            viewModelScope.launch { _effect.emit(PollDetailsEffect.ShowError("User not logged in. Cannot vote.")) }
            return
        }
        if (_uiState.value.userVote != null) {
             viewModelScope.launch { _effect.emit(PollDetailsEffect.ShowError("You have already voted on this poll.")) }
            return
        }

        viewModelScope.launch {
            _uiState.update { it.copy(isSubmittingVote = true, error = null) }

            val newVote = PollVote(
                pollId = currentPoll.pollId,
                voterId = currentUser.uid,
                optionId = currentSelectedOptionId
            )

            when (val result = pollsRepository.submitVote(newVote)) {
                is Resource.Success -> {
                    _uiState.update {
                        it.copy(
                            isSubmittingVote = false,
                            userVote = newVote,
                            selectedOptionId = null
                        )
                    }
                    _effect.emit(PollDetailsEffect.VoteSubmittedSuccessfully)
                }
                is Resource.Error -> {
                    _uiState.update {
                        it.copy(
                            isSubmittingVote = false,
                            error = result.message ?: "Failed to submit vote. Please try again."
                        )
                    }
                    _effect.emit(PollDetailsEffect.ShowError(result.message ?: "Failed to submit vote."))
                }
                is Resource.Loading -> {
                    _uiState.update { it.copy(isSubmittingVote = true) }
                }
            }
        }
    }

    // New function to observe invited friends vote status
    private fun observeInvitedFriendsVoteStatus(currentPollId: String) {
        viewModelScope.launch {
            pollsRepository.getInvitedFriendsVoteStatus(currentPollId)
                .collect { result ->
                    when (result) {
                        is Resource.Success -> {
                            _uiState.update { it.copy(invitedFriendsVoteStatus = result.data ?: emptyList()) }
                        }
                        is Resource.Error -> {
                            // Optionally handle error for this specific flow, e.g., log or show a non-critical message
                            _uiState.update { it.copy(invitedFriendsVoteStatus = emptyList()) } // Clear on error
                        }
                        is Resource.Loading -> {
                            // Can be ignored if not showing specific loading for this list
                        }
                    }
                }
        }
    }
}