package app.donskey.cantdecide.ui.friends

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.provider.ContactsContract
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.donskey.cantdecide.data.models.Friend
import app.donskey.cantdecide.data.models.User
import app.donskey.cantdecide.data.repositories.FriendsRepository
import app.donskey.cantdecide.data.repositories.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.launchIn

// Simple data class to hold contact info
data class DeviceContact(
    val id: String, // Use contact ID as a stable identifier
    val name: String?,
    val phoneNumber: String?
)

/**
 * Data class holding a Friend object and their associated User profile.
 */
data class FriendWithProfile(
    val friend: Friend,
    val profile: User? // Profile can be null if fetch fails or user not found
)

/**
 * Data class holding an incoming Friend request object and the sender's User profile.
 */
data class RequestWithProfile(
    val request: Friend, // The Friend object representing the request
    val senderProfile: User? // Profile of the user who initiated the request
)

/**
 * ViewModel for the Friends screen, handling friend list and friend requests.
 */
@HiltViewModel
class FriendsViewModel @Inject constructor(
    private val friendsRepository: FriendsRepository,
    private val userRepository: UserRepository
) : ViewModel() {

    companion object {
        private const val TAG = "FriendsViewModel"
    }

    // Repositories are now injected
    // private val friendsRepository = FriendsRepository() // Removed direct instantiation
    // private val userRepository = UserRepository() // Removed direct instantiation

    // Single source of truth for the UI state
    private val _uiState = MutableStateFlow(FriendsUiState(isLoading = true)) // Initialize with loading
    val uiState: StateFlow<FriendsUiState> = _uiState.asStateFlow() // Expose as StateFlow

    // Get the current user ID once - can use injected userRepository
    private val currentUserId: String? = userRepository.getCurrentUser()?.uid

    init {
        // Launch the flow combination in the init block
        viewModelScope.launch {
            combineAndFetchProfiles()
        }
        // Keep index trigger if needed
        triggerIndexQueries()
    }

    // New function to handle the combination and profile fetching
    private fun combineAndFetchProfiles() {
        // Base flows from the repository
        val friendsFlow: Flow<List<Friend>> = friendsRepository.getCurrentUserFriends()
            .catch { e ->
                Log.e(TAG, "Error in friends flow: ${e.message}", e)
                _uiState.update { it.copy(error = "Failed to load friends: ${e.message}", isLoading = false) }
                emit(emptyList()) // Emit empty list on error
            }

        val incomingRequestsFlow: Flow<List<Friend>> = friendsRepository.getIncomingFriendRequests()
            .catch { e ->
                Log.e(TAG, "Error in incoming requests flow: ${e.message}", e)
                _uiState.update { it.copy(error = "Failed to load incoming requests: ${e.message}", isLoadingRequests = false) }
                emit(emptyList()) // Emit empty list on error
            }

        val outgoingRequestsFlow: Flow<List<Friend>> = friendsRepository.getOutgoingPendingRequestsFlow()
            .catch { e ->
                Log.e(TAG, "Error in outgoing requests flow: ${e.message}", e)
                _uiState.update { it.copy(error = "Failed to load outgoing requests: ${e.message}", isLoadingOutgoingRequests = false) }
                emit(emptyList()) // Emit empty list on error
            }

        // Combine the base flows to get the lists
        combine(friendsFlow, incomingRequestsFlow, outgoingRequestsFlow) { friends, incomingRequests, outgoingRequests ->
            Log.d(TAG, "Combine triggered. Friends: ${friends.size}, Incoming Requests: ${incomingRequests.size}, Outgoing Requests: ${outgoingRequests.size}")

            // Determine all unique user IDs needed for profiles
            val friendUserIds = buildSet {
                friends.forEach { friend ->
                    val otherUserId = friend.getOtherUserId(currentUserId ?: "")
                    if (otherUserId.isNotBlank()) add(otherUserId)
                }
            }
            val incomingRequestSenderIds = buildSet {
                incomingRequests.forEach { request ->
                    if (request.initiatedBy.isNotBlank()) add(request.initiatedBy)
                }
            }
            // For outgoing requests, the recipient is userId2
            val outgoingRequestRecipientIds = buildSet {
                outgoingRequests.forEach { request ->
                    if (request.userId2.isNotBlank()) add(request.userId2)
                }
            }

            val allUserIds = (friendUserIds + incomingRequestSenderIds + outgoingRequestRecipientIds).toList()

            // Fetch profiles for these IDs
            val profilesMap = if (allUserIds.isNotEmpty()) {
                try {
                    Log.d(TAG, "Fetching profiles for IDs: $allUserIds")
                    userRepository.getUserProfilesMap(allUserIds)
                } catch (e: Exception) {
                    Log.e(TAG, "Error fetching profiles map: ${e.message}", e)
                     _uiState.update { it.copy(error = "Failed to load user profiles: ${e.message}") }
                    emptyMap() // Return empty map on error
                }
            } else {
                Log.d(TAG, "No user IDs require profile fetching.")
                emptyMap()
            }
            Log.d(TAG, "Profiles map fetched: ${profilesMap.size} profiles")


            // Combine friends with their profiles
            val friendsWithProfiles = friends.map { friend ->
                val friendUserId = friend.getOtherUserId(currentUserId ?: "")
                FriendWithProfile(friend, profilesMap[friendUserId])
            }

            // --- Sort friendsWithProfiles alphabetically by display name ---
            val sortedFriendsWithProfiles = friendsWithProfiles.sortedWith(compareBy(String.CASE_INSENSITIVE_ORDER) {
                it.profile?.displayName?.takeIf { dn -> dn.isNotBlank() } ?: "zzz_fallback_name" // Fallback for robust sorting
            })
            // --- End sorting ---

            // Combine incoming requests with sender profiles
            val incomingRequestsWithProfiles = incomingRequests.map { request ->
                RequestWithProfile(request, profilesMap[request.initiatedBy])
            }

            // Combine outgoing requests with recipient profiles
            // Reusing RequestWithProfile: 'request' is the Friend object, 'senderProfile' will hold recipient's profile
            val outgoingRequestsWithProfiles = outgoingRequests.map { request ->
                RequestWithProfile(request, profilesMap[request.userId2])
            }

            // Update the single _uiState with the combined data and set loading false
            _uiState.update { currentState ->
                 Log.d(TAG, "Updating final UI state. Friends: ${sortedFriendsWithProfiles.size}, Incoming: ${incomingRequestsWithProfiles.size}, Outgoing: ${outgoingRequestsWithProfiles.size}")
                currentState.copy(
                    isLoading = false,
                    isLoadingRequests = false,
                    isLoadingOutgoingRequests = false,
                    friends = sortedFriendsWithProfiles,
                    incomingRequests = incomingRequestsWithProfiles,
                    outgoingRequestsWithProfiles = outgoingRequestsWithProfiles,
                    currentLoggedInUserId = this.currentUserId,
                    error = if (currentState.error?.contains("Failed to load user profiles") == true && profilesMap.isNotEmpty()) null else currentState.error
                )
            }
        }.launchIn(viewModelScope) // Launch the collection within the scope
    }

    /**
     * This function is used to trigger the queries that will generate the index links.
     * It's only needed during development to create the required indexes.
     */
    private fun triggerIndexQueries() {
        viewModelScope.launch {
            try {
                // These calls will fail with index required errors in logs
                // which can be used to create the indexes
                // Using launchIn to keep the flows active briefly for query trigger
                friendsRepository.getCurrentUserFriends().launchIn(this)
                friendsRepository.getIncomingFriendRequests().launchIn(this)
                friendsRepository.getOutgoingPendingRequestsFlow().launchIn(this)

                Log.d(TAG, "Index queries triggered via active flows.")
            } catch (e: Exception) {
                Log.e(TAG, "Error triggering index queries: ${e.message}")
            }
        }
    }

    /**
     * Send a friend request to a user.
     */
    private fun sendFriendRequest(userId: String, phoneNumber: String = "") {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isSending = true) } // Use update
                val success = friendsRepository.sendFriendRequest(userId, phoneNumber)
                if (success) {
                    // Successfully sent, just update sending state.
                    // The calling function (e.g., handleContactSelection) is responsible for specific success messages.
                    _uiState.update { it.copy(isSending = false) }
                } else {
                    _uiState.update { it.copy(isSending = false, error = "Failed to send friend request") }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error sending friend request: ${e.message}")
                _uiState.update { it.copy(isSending = false, error = "Error: ${e.message}") }
            }
        }
    }

    /**
     * Accept a friend request.
     */
    fun acceptFriendRequest(friendshipId: String) {
        viewModelScope.launch {
            try {
                // Optionally show temporary accepting state if needed
                val success = friendsRepository.acceptFriendRequest(friendshipId)
                if (!success) {
                    _uiState.update { it.copy(error = "Failed to accept friend request") }
                }
                // Message could be added here or rely on list update
            } catch (e: Exception) {
                Log.e(TAG, "Error accepting friend request: ${e.message}")
                _uiState.update { it.copy(error = "Error accepting request: ${e.message}") }
            }
        }
    }

    /**
     * Reject a friend request.
     */
    fun rejectFriendRequest(friendshipId: String) {
        viewModelScope.launch {
            try {
                val success = friendsRepository.rejectFriendRequest(friendshipId)
                 if (!success) {
                    _uiState.update { it.copy(error = "Failed to reject friend request") }
                }
                // Message could be added here or rely on list update
            } catch (e: Exception) {
                Log.e(TAG, "Error rejecting friend request: ${e.message}")
                 _uiState.update { it.copy(error = "Error rejecting request: ${e.message}") }
            }
        }
    }

    /**
     * Remove a friend.
     */
     fun removeFriend(friendshipId: String) {
         viewModelScope.launch {
             try {
                 val success = friendsRepository.removeFriend(friendshipId)
                 if (!success) {
                     _uiState.update { it.copy(error = "Failed to remove friend") }
                 }
                 // Optionally show success message via snackbar state
                 // _uiState.update { it.copy(friendRequestStatusMessage = "Friend removed.") }
             } catch (e: Exception) {
                 Log.e(TAG, "Error removing friend: ${e.message}", e)
                 _uiState.update { it.copy(error = "Error removing friend: ${e.message}") }
             }
         }
     }

    // Function to be called by the UI to initiate contact loading
    fun requestContactsPermissionAndLoad(permissionLauncher: ActivityResultLauncher<String>, context: Context) {
        when (PackageManager.PERMISSION_GRANTED) {
            ContextCompat.checkSelfPermission(
                context,
                Manifest.permission.READ_CONTACTS
            ) -> {
                // Permission already granted, load contacts
                Log.d(TAG, "Contacts permission already granted. Loading contacts...")
                loadDeviceContacts(context)
            }
            else -> {
                // Request permission
                Log.d(TAG, "Requesting contacts permission.")
                permissionLauncher.launch(Manifest.permission.READ_CONTACTS)
            }
        }
    }

    // Function to load contacts after permission is granted (or if already granted)
    fun loadDeviceContacts(context: Context) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoadingContacts = true, contactsError = null) }
            try {
                Log.d(TAG, "Loading contacts from ContentResolver...")
                // Load contacts in background using Dispatchers.IO
                val contacts = fetchDeviceContacts(context)
                Log.d(TAG, "Loaded ${contacts.size} contacts.")
                _uiState.update {
                    // Update state on success, set hasSearchedContacts to true
                    it.copy(deviceContacts = contacts, isLoadingContacts = false, hasSearchedContacts = true)
                }
            } catch (e: SecurityException) {
                 Log.e(TAG, "SecurityException loading contacts: ${e.message}", e)
                 _uiState.update {
                    // Update state on error, set hasSearchedContacts to true
                    it.copy(
                        isLoadingContacts = false,
                        contactsError = "Permission denied. Cannot load contacts.",
                        hasSearchedContacts = true
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading contacts: ${e.message}", e)
                _uiState.update {
                    // Update state on other errors, set hasSearchedContacts to true
                    it.copy(
                        isLoadingContacts = false,
                        contactsError = "Failed to load contacts: ${e.message}",
                        hasSearchedContacts = true
                    )
                }
            }
        }
    }

    // Private suspend function to fetch contacts using ContentResolver
    private suspend fun fetchDeviceContacts(context: Context): List<DeviceContact> = withContext(Dispatchers.IO) {
        val contactsList = mutableListOf<DeviceContact>()
        val contentResolver = context.contentResolver
        val projection = arrayOf(
            ContactsContract.CommonDataKinds.Phone._ID, // Use Phone._ID for contact identifier
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME,
            ContactsContract.CommonDataKinds.Phone.NUMBER
        )
        val cursor: android.database.Cursor? = contentResolver.query(
            ContactsContract.CommonDataKinds.Phone.CONTENT_URI,
            projection,
            null, // No selection filter needed for all contacts with phone numbers
            null,
            ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME + " ASC" // Order by name
        )

        cursor?.use { // Ensure cursor is closed
            val idColumn = it.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.Phone._ID)
            val nameColumn = it.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.Phone.DISPLAY_NAME)
            val numberColumn = it.getColumnIndexOrThrow(ContactsContract.CommonDataKinds.Phone.NUMBER)

            while (it.moveToNext()) {
                val id = it.getString(idColumn)
                val name = it.getString(nameColumn)
                val number = it.getString(numberColumn)
                // Basic normalization: remove spaces and hyphens - Fix escape sequence
                val normalizedNumber = number?.replace(Regex("[\\s-]"), "")
                if (!normalizedNumber.isNullOrBlank()) {
                    contactsList.add(DeviceContact(id = id, name = name, phoneNumber = normalizedNumber))
                }
            }
        }
        // Deduplicate based on normalized phone number, keeping the first name found
        return@withContext contactsList.distinctBy { it.phoneNumber }
    }

    /**
     * Handles the selection of a contact from the device list.
     * Searches for the user in Firestore and initiates either a friend request or SMS flow.
     * @param contact The selected DeviceContact.
     */
    fun handleContactSelection(contact: DeviceContact) {
        val phoneNumber = contact.phoneNumber
        if (phoneNumber.isNullOrBlank()) {
            Log.w(TAG, "Selected contact has no phone number: ${contact.name}")
            // Optionally update UI state with a specific error for this contact
             _uiState.update { it.copy(error = "Selected contact has no phone number.") }
            return
        }

        // Indicate loading/searching state specifically for this action if needed
        // _uiState.update { it.copy(isSearchingContact = true) }

        viewModelScope.launch {
            try {
                Log.d(TAG, "Handling selection for contact: ${contact.name}, phone: $phoneNumber")
                val foundUser = userRepository.findUserByPhoneNumber(phoneNumber)

                if (foundUser != null) {
                    // User found in Firestore
                    Log.d(TAG, "User found for contact ${contact.name}: ${foundUser.uid}")

                    // Check if already friends or request pending
                    val existingFriendship = friendsRepository.findExistingFriendship(
                        userId1 = currentUserId ?: "",
                        userId2 = foundUser.uid
                    )

                    if (existingFriendship == null) {
                        // No existing relationship, send friend request
                        Log.d(TAG, "No existing friendship found. Sending request to ${foundUser.uid}")
                        sendFriendRequest(foundUser.uid, phoneNumber)
                        // Update UI state to show success message
                        _uiState.update { it.copy(friendRequestStatusMessage = "Friend request sent to ${contact.name}.") }
                    } else {
                        // Friendship exists or is pending
                        val statusMessage = when (existingFriendship.status) {
                            "pending" -> "Friend request already pending for ${contact.name}."
                            "accepted" -> "You are already friends with ${contact.name}."
                            else -> "Friendship status: ${existingFriendship.status}." // Handle other potential statuses
                        }
                        Log.d(TAG, "Friendship status for ${foundUser.uid}: ${existingFriendship.status}")
                        // Update UI state to show existing status message
                        _uiState.update { it.copy(friendRequestStatusMessage = statusMessage) }
                    }

                } else {
                    // User not found in Firestore - Initiate SMS flow
                    Log.d(TAG, "User not found for contact ${contact.name}. Initiate SMS flow.")
                    // TODO: Implement SMS sending logic
                    // For now, update UI state or show a Toast
                    // Update friendRequestStatusMessage for Snackbar feedback
                     _uiState.update { it.copy(friendRequestStatusMessage = "User not found. Please ask ${contact.name ?: "this contact"} to install the app. You can share this link: https://your-app-install-link.com") } // Update correct field
                }

            } catch (e: Exception) {
                Log.e(TAG, "Error handling contact selection for $phoneNumber: ${e.message}", e)
                 _uiState.update { it.copy(error = "Error processing contact: ${e.message}") }
            } finally {
                 // Reset any specific loading state if used
                 // _uiState.update { it.copy(isSearchingContact = false) }
            }
        }
    }

    /**
     * Clear the friend request status message.
     */
    fun clearFriendRequestStatusMessage() {
        _uiState.update { it.copy(friendRequestStatusMessage = null) }
    }
}

/**
 * Represents the UI state for the Friends screen.
 */
data class FriendsUiState(
    val isLoading: Boolean = false,
    val isLoadingRequests: Boolean = false, // Kept separate for now, can merge with isLoading
    val isLoadingOutgoingRequests: Boolean = false, // Added for outgoing requests
    val isSending: Boolean = false,
    val friends: List<FriendWithProfile> = emptyList(),
    val incomingRequests: List<RequestWithProfile> = emptyList(),
    val outgoingRequestsWithProfiles: List<RequestWithProfile> = emptyList(), // For outgoing requests with recipient profiles
    val error: String? = null,
    val friendRequestStatusMessage: String? = null,
    val isLoadingContacts: Boolean = false,
    val deviceContacts: List<DeviceContact> = emptyList(),
    val contactsError: String? = null,
    val hasSearchedContacts: Boolean = false,
    val currentLoggedInUserId: String? = null
)