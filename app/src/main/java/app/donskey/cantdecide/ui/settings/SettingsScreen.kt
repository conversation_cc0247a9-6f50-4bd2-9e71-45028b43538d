package app.donskey.cantdecide.ui.settings

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.AccountCircle
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.ChatBubbleOutline
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Description
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Lock
import androidx.compose.material.icons.filled.Notifications
import androidx.compose.material.icons.filled.Palette
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.VerifiedUser
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.compose.currentBackStackEntryAsState
import coil.compose.AsyncImage
import coil.request.ImageRequest
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.ui.graphics.Color
import kotlinx.coroutines.delay
import android.widget.Toast
import android.util.Log
import android.Manifest
import android.content.pm.PackageManager
import android.os.Build
import androidx.core.content.ContextCompat
import app.donskey.cantdecide.R
import app.donskey.cantdecide.ui.theme.AppTheme
import app.donskey.cantdecide.ui.theme.ThemeManager

/**
 * Main Settings screen container with nested navigation
 * This allows for WhatsApp-style navigation between settings view and edit screens
 */
@Composable
fun SettingsScreenContainer(
    navController: NavHostController = rememberNavController()
) {
    val settingsViewModel: SettingsViewModel = hiltViewModel()

    NavHost(navController = navController, startDestination = "settings_view") {
        composable("settings_view") {
            SettingsScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        composable("edit_display_name") {
            EditDisplayNameScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        composable("edit_bio") {
            EditBioScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        composable("edit_email") {
            EditEmailScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        composable("edit_photo") {
            EditPhotoScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        // New screens for settings
        composable("notification_preferences") {
            NotificationPreferencesScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        composable("privacy_settings") {
            PrivacySettingsScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        composable("appearance_settings") {
            AppearanceSettingsScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        composable("account_settings") {
            AccountSettingsScreen(settingsViewModel = settingsViewModel, navController = navController)
        }
        // Data privacy screen
        composable("data_privacy") {
            DataPrivacyScreen(navController = navController)
        }
        // Placeholder screens for friends and following
        composable("friends_list") {
            PlaceholderScreen(title = "Friends", navController = navController)
        }
        composable("following_list") {
            PlaceholderScreen(title = "Following", navController = navController)
        }
    }
}

/**
 * Composable function for the Settings Screen.
 * Displays user profile information and provides navigation to edit screens.
 *
 * @param settingsViewModel ViewModel instance for managing settings state and actions.
 * @param navController Navigation controller for navigating between screens.
 */
@Composable
fun SettingsScreen(
    settingsViewModel: SettingsViewModel = viewModel(),
    navController: NavController
) {
    // Collect the UI state from the ViewModel
    val uiState by settingsViewModel.uiState.collectAsState()
    val user = uiState.user
    val isLoading = uiState.isLoading
    // Create a scroll state that can be remembered
    val scrollState = rememberScrollState()
    val context = LocalContext.current // Get context for sign out

    // State for controlling the general error dialog
    var showErrorDialog by remember { mutableStateOf(false) }
    var dialogErrorMessage by remember { mutableStateOf("") }

    val currentBackStackEntry by navController.currentBackStackEntryAsState()
    val isSettingsViewActive = currentBackStackEntry?.destination?.route == "settings_view"

    LaunchedEffect(isSettingsViewActive) {
        if (isSettingsViewActive) {
            // When SettingsScreen becomes active, ensure its dialog is not showing
            // and clear any errors from the ViewModel that might have been set by a previous screen.
            Log.d("SettingsScreen", "SettingsView active. Clearing VM error. Hiding local dialog.")
            settingsViewModel.clearErrorMessage()
            showErrorDialog = false
            dialogErrorMessage = ""
        }
    }

    // Collect events from the dedicated flow to show the main screen's error dialog
    LaunchedEffect(Unit) {
        settingsViewModel.showSettingsScreenErrorEvent.collect { errorMessage ->
            dialogErrorMessage = errorMessage
            showErrorDialog = true
        }
    }

    // State for controlling the verification sent dialog
    var showVerificationDialog by remember { mutableStateOf(false) }

    // Collect the event to show the verification dialog
    LaunchedEffect(Unit) {
        settingsViewModel.showVerificationSentDialogEvent.collect {
            // Ensure we don't show this if already verified somehow
            if (!settingsViewModel.isEmailVerified) {
                showVerificationDialog = true
            }
        }
    }

    // --- General Error Dialog ---
    if (showErrorDialog) {
        AlertDialog(
            onDismissRequest = {
                showErrorDialog = false
                // Clear the error message from the *local* state when dismissing
                // Don't clear the ViewModel's primary errorMessage here, as it might be for inline display
                dialogErrorMessage = ""
            },
            title = { Text("Error") },
            text = { Text(dialogErrorMessage) },
            confirmButton = {
                TextButton(onClick = {
                    showErrorDialog = false
                    dialogErrorMessage = ""
                }) {
                    Text("OK")
                }
            }
        )
    }

    // --- Verification Sent Dialog (for clicks on Verify link) ---
    if (showVerificationDialog) {
        AlertDialog(
            onDismissRequest = {
                // For now, force OK click to sign out
            },
            title = { Text("Verification Email Sent") },
            text = { Text("Please check your email (including spam folder) to verify your address. You will be signed out now to complete the process.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showVerificationDialog = false
                        settingsViewModel.signOut(context) // Sign out and navigate
                    }
                ) {
                    Text("OK")
                }
            }
        )
    }
    // --- End Dialog ---

    // Display loading indicator if data is loading
    if (isLoading) {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
            CircularProgressIndicator()
        }
        return // Exit composable if loading
    }

    // Display profile content when not loading (we'll handle errors with the dialog now)
    Scaffold { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(bottom = 80.dp), // Extra padding to account for bottom navigation
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(24.dp))

            // Profile Image - Directly place the profile image without the gradient background
            Box(
                modifier = Modifier.padding(top = 16.dp),
                contentAlignment = Alignment.Center
            ) {
                // Profile photo with outer ring
                Box(
                    modifier = Modifier
                        .size(130.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.background)
                        .padding(4.dp),
                    contentAlignment = Alignment.Center
                ) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(user.photoUrl.ifEmpty { null })
                            .crossfade(true)
                            .build(),
                        contentDescription = "Profile Picture",
                        modifier = Modifier
                            .size(120.dp)
                            .clip(CircleShape)
                            .clickable { navController.navigate("edit_photo") },
                        contentScale = ContentScale.Crop
                    )

                    // Show themed placeholder if photoUrl is empty
                    if (user.photoUrl.isEmpty()) {
                        Box(
                            modifier = Modifier
                                .size(120.dp)
                                .clip(CircleShape)
                                .background(MaterialTheme.colorScheme.primaryContainer)
                                .clickable { navController.navigate("edit_photo") },
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = "Default Profile Icon",
                                tint = MaterialTheme.colorScheme.onPrimaryContainer,
                                modifier = Modifier.size(72.dp)
                            )
                        }
                    }
                }

                // Edit button overlaid on image
                Box(
                    modifier = Modifier
                        .size(36.dp)
                        .align(Alignment.BottomEnd)
                        .offset(x = (-8).dp, y = (-8).dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.secondary)
                        .clickable { navController.navigate("edit_photo") },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "Edit Profile Picture",
                        tint = MaterialTheme.colorScheme.onSecondary,
                        modifier = Modifier.size(20.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Display Name Text
            Text(
                text = user.displayName.ifBlank { "Username" },
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold
            )
            Spacer(modifier = Modifier.height(4.dp))

            // Bio Text
            Text(
                text = user.bio.ifBlank { "No bio yet" },
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 24.dp),
                textAlign = TextAlign.Center,
                maxLines = 4,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Following and Friends Section (Task 3h) - Moved here
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                contentAlignment = Alignment.Center
            ) {
                Row(
                    modifier = Modifier.padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Following section with better touch target
                    Box(
                        modifier = Modifier
                            .clickable { navController.navigate("following_list") }
                            .padding(8.dp)
                    ) {
                        Text(
                            text = "Following ${user.friendsCount}",
                            style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp),
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // Divider between counts
                    Text(
                        text = " | ",
                        style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp),
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )

                    // Friends section with better touch target
                    Box(
                        modifier = Modifier
                            .clickable { navController.navigate("friends_list") }
                            .padding(8.dp)
                    ) {
                        Text(
                            text = "Friends ${user.friendsCount}",
                            style = MaterialTheme.typography.titleMedium.copy(fontSize = 18.sp),
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // --- Stats Bar (Task 3g) ---
            ProfileStatsCard(
                pollsCreated = user.pollsCreated, // Assuming User model has these fields (using 0 for now)
                votesReceived = user.votesReceived,
                decisionsMade = user.decisionsCount
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Profile Settings Card - Elevated card with modern styling
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface // Changed from surfaceContainerLow
                )
            ) {
                Column(modifier = Modifier.fillMaxWidth()) {
                    // Display Name Item
                    ProfileSettingItem(
                        title = "Display Name",
                        subtitle = user.displayName.ifBlank { "Add a display name" },
                        onClick = { navController.navigate("edit_display_name") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Person,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    )

                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = 16.dp),
                        thickness = 0.5.dp,
                        color = MaterialTheme.colorScheme.outlineVariant
                    )

                    // Bio Item
                    ProfileSettingItem(
                        title = "Bio",
                        subtitle = user.bio.ifBlank { "Add a bio" },
                        onClick = { navController.navigate("edit_bio") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Description,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    )

                    HorizontalDivider(
                        modifier = Modifier.padding(horizontal = 16.dp),
                        thickness = 0.5.dp,
                        color = MaterialTheme.colorScheme.outlineVariant
                    )

                    // Email Item with Verification Status
                    ProfileSettingItem(
                        title = "Email",
                        subtitle = user.email.ifBlank { "Add an email" },
                        onClick = { navController.navigate("edit_email") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Email,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        },
                        trailingContent = {
                            if (user.email.isNotBlank()) {
                                if (settingsViewModel.isEmailVerified) {
                                    Box(
                                        modifier = Modifier
                                            .background(
                                                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                                shape = RoundedCornerShape(4.dp)
                                            )
                                            .padding(horizontal = 8.dp, vertical = 4.dp)
                                    ) {
                                        Text(
                                            text = "Verified",
                                            color = MaterialTheme.colorScheme.primary,
                                            style = MaterialTheme.typography.bodySmall,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                } else {
                                    Box(
                                        modifier = Modifier
                                            .background(
                                                color = MaterialTheme.colorScheme.primary.copy(alpha = 0.1f),
                                                shape = RoundedCornerShape(4.dp)
                                            )
                                            .clickable { settingsViewModel.sendVerificationEmail() }
                                            .padding(horizontal = 8.dp, vertical = 4.dp)
                                    ) {
                                        Text(
                                            text = if (settingsViewModel.isVerificationSent) "Verification Sent" else "Verify",
                                            color = MaterialTheme.colorScheme.primary,
                                            style = MaterialTheme.typography.bodySmall,
                                            fontWeight = FontWeight.Medium
                                        )
                                    }
                                }
                            }
                        }
                    )
                }
            }

            // Additional settings card
            Spacer(modifier = Modifier.height(16.dp))

            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 4.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surface // Changed from surfaceContainerLow
                )
            ) {
                Column(modifier = Modifier.fillMaxWidth()) {
                    // Notifications Preferences
                    ProfileSettingItem(
                        title = "Notifications Preferences",
                        subtitle = "Manage app notifications",
                        onClick = { navController.navigate("notification_preferences") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Notifications,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    )

                    HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp))

                    // Privacy Settings
                    ProfileSettingItem(
                        title = "Privacy Settings",
                        subtitle = "Manage your privacy preferences",
                        onClick = { navController.navigate("privacy_settings") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Lock,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    )

                    HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp))

                    // Appearance
                    ProfileSettingItem(
                        title = "Appearance",
                        subtitle = "Customize app appearance",
                        onClick = { navController.navigate("appearance_settings") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Palette,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    )

                    HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp))

                    // Account
                    ProfileSettingItem(
                        title = "Account",
                        subtitle = "Manage your account",
                        onClick = { navController.navigate("account_settings") },
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.AccountCircle,
                                contentDescription = null,
                                tint = MaterialTheme.colorScheme.primary
                            )
                        }
                    )
                }
            }

            // TODO: Add sections for active polls, past decisions
            Spacer(modifier = Modifier.height(38.dp))

            // Add divider above Active Polls section
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = 16.dp),
                thickness = 1.dp,
                color = MaterialTheme.colorScheme.outlineVariant
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Active Polls Section Header (placeholder for Task 3i)
            Text(
                text = "Active Polls",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Active Polls placeholder (Task 3i)
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // Poll title
                    Text(
                        text = "What should I have for dinner tonight?",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    // Poll description
                    Text(
                        text = "Help me decide what to eat",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Poll options with progress bars
                    PollOptionItem(option = "Pizza", percentage = 14)
                    PollOptionItem(option = "Sushi", percentage = 29)
                    PollOptionItem(option = "Pasta", percentage = 20)
                    PollOptionItem(option = "Salad", percentage = 37)

                    Spacer(modifier = Modifier.height(8.dp))

                    // Vote count
                    Text(
                        text = "8 votes",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.align(Alignment.End)
                    )
                }
            }

            // Second active poll - Movie poll
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(bottom = 16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // Poll title
                    Text(
                        text = "Which movie should we watch?",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    // Poll description
                    Text(
                        text = "Weekend movie night options",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.padding(vertical = 4.dp)
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Poll options with progress bars
                    PollOptionItem(option = "Inception", percentage = 13)
                    PollOptionItem(option = "The Matrix", percentage = 30)
                    PollOptionItem(option = "Interstellar", percentage = 56)

                    Spacer(modifier = Modifier.height(8.dp))

                    // Vote count
                    Text(
                        text = "46 votes",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.align(Alignment.End)
                    )
                }
            }

            // Past Decisions Section Header
            Spacer(modifier = Modifier.height(24.dp))

            // Add divider above Past Decisions section
            HorizontalDivider(
                modifier = Modifier.padding(horizontal = 16.dp),
                thickness = 1.dp,
                color = MaterialTheme.colorScheme.outlineVariant
            )

            Spacer(modifier = Modifier.height(24.dp))

            Text(
                text = "Past Decisions",
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Past Decisions placeholder cards
            PastDecisionCard(
                title = "Best vacation destination?",
                description = "Summer 2024 planning",
                decision = "Hawaii",
                votes = 24
            )

            Spacer(modifier = Modifier.height(16.dp))

            PastDecisionCard(
                title = "New phone choice",
                description = "Time for an upgrade",
                decision = "iPhone",
                votes = 48
            )

            Spacer(modifier = Modifier.height(16.dp))

            PastDecisionCard(
                title = "Favorite color?",
                description = "Repainting my room",
                decision = "Green",
                votes = 27
            )

            // Extra space at the bottom
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

/**
 * A reusable settings item component that looks like a WhatsApp settings item
 */
@Composable
fun ProfileSettingItem(
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingContent: @Composable (() -> Unit)? = null
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Add leading icon if provided
        if (leadingIcon != null) {
            Box(modifier = Modifier.padding(end = 16.dp)) {
                leadingIcon()
            }
        }

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(end = 8.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleSmall,
                fontWeight = FontWeight.Medium
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = subtitle,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }

        // If there's custom trailing content, show it, otherwise show arrow
        if (trailingContent != null) {
            trailingContent()
            Spacer(modifier = Modifier.width(8.dp))
        }

        Icon(
            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
            contentDescription = "Navigate",
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Card composable to display user statistics.
 * Uses placeholder values for now.
 *
 * @param pollsCreated Number of polls created by the user.
 * @param votesReceived Number of votes received by the user.
 * @param decisionsMade Number of decisions made (completed polls).
 */
@Composable
fun ProfileStatsCard(
    pollsCreated: Int = 0, // Default to 0 for placeholder
    votesReceived: Int = 0,
    decisionsMade: Int = 0
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface) // Changed from surfaceContainerLow
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.SpaceAround, // Distribute items evenly
            verticalAlignment = Alignment.CenterVertically
        ) {
            StatItem(label = "Polls Created", value = pollsCreated.toString())
            StatItem(label = "Votes Received", value = votesReceived.toString())
            StatItem(label = "Decisions Made", value = decisionsMade.toString())
        }
    }
}

/**
 * Stats item displayed in the stats card
 */
@Composable
fun StatItem(label: String, value: String) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier.padding(vertical = 8.dp)
    ) {
        Text(
            text = value,
            style = MaterialTheme.typography.titleLarge,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = label,
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * Screen for editing display name
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditDisplayNameScreen(
    settingsViewModel: SettingsViewModel,
    navController: NavController
) {
    val uiState by settingsViewModel.uiState.collectAsState()
    val currentUserDisplayName = uiState.user.displayName
    val isLoading = uiState.isLoading

    var isDisplayNameEditable by remember { mutableStateOf(false) }
    var tempDisplayName by remember(currentUserDisplayName, settingsViewModel.tempDisplayName, isDisplayNameEditable) {
        mutableStateOf(if (isDisplayNameEditable) settingsViewModel.tempDisplayName else currentUserDisplayName)
    }
    var isDisplayNameError by remember { mutableStateOf(false) }
    val scrollState = rememberScrollState()

    // Update ViewModel's tempDisplayName whenever our local tempDisplayName changes *while in edit mode*
    LaunchedEffect(tempDisplayName, isDisplayNameEditable) {
        if (isDisplayNameEditable) {
            settingsViewModel.tempDisplayName = tempDisplayName
        }
    }

    // Reset edit mode and temp field on disposal or when navigating away
    DisposableEffect(Unit) {
        onDispose {
            // settingsViewModel.tempDisplayName = uiState.user.displayName // ViewModel handles its temp state
            isDisplayNameEditable = false
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Edit Display Name") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }, enabled = !isLoading) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                actions = {
                    if (isDisplayNameEditable) {
                        IconButton(
                            onClick = {
                                if (tempDisplayName.isBlank()) {
                                    isDisplayNameError = true
                                } else {
                                    settingsViewModel.saveProfileChanges()
                                    isDisplayNameEditable = false // Exit edit mode
                                    // navController.navigateUp() // Consider if navigation should happen here or be tied to save success
                                }
                            },
                            enabled = !isLoading
                        ) {
                            if (isLoading) {
                                CircularProgressIndicator(modifier = Modifier.size(24.dp), strokeWidth = 2.dp)
                            } else {
                                Icon(Icons.Default.Check, contentDescription = "Save Display Name")
                            }
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(16.dp)
                .imePadding() // Handle keyboard insets
        ) {
            OutlinedTextField(
                value = tempDisplayName,
                onValueChange = {
                    tempDisplayName = it
                    if (isDisplayNameError && it.isNotBlank()) {
                        isDisplayNameError = false
                    }
                    if (uiState.errorMessage != null) { // Clear ViewModel error if user types
                        settingsViewModel.clearErrorMessage()
                    }
                },
                label = { Text("Display Name") },
                modifier = Modifier.fillMaxWidth(),
                enabled = isDisplayNameEditable,
                readOnly = !isDisplayNameEditable,
                isError = isDisplayNameError || !uiState.errorMessage.isNullOrBlank() && isDisplayNameEditable,
                supportingText = {
                    if (isDisplayNameError) {
                        Text("Display name cannot be empty")
                    } else if (!uiState.errorMessage.isNullOrBlank() && isDisplayNameEditable) {
                        Text(uiState.errorMessage!!, color = MaterialTheme.colorScheme.error)
                    } else if (!isDisplayNameEditable) {
                        Text(if (tempDisplayName.isBlank()) "Tap the edit icon to add a display name" else "Tap the edit icon to change your display name")
                    } else {
                        Text("Enter your name")
                    }
                },
                singleLine = true,
                keyboardOptions = KeyboardOptions(
                    imeAction = ImeAction.Done
                ),
                trailingIcon = {
                    if (!isDisplayNameEditable && !isLoading) {
                        IconButton(onClick = {
                            // When starting to edit, ensure tempDisplayName reflects the current actual display name
                            val currentActualDisplayName = settingsViewModel.uiState.value.user.displayName
                            settingsViewModel.tempDisplayName = currentActualDisplayName
                            tempDisplayName = currentActualDisplayName
                            isDisplayNameEditable = true
                            settingsViewModel.clearErrorMessage() // Clear any previous errors
                        }) {
                            Icon(Icons.Default.Edit, contentDescription = "Edit Display Name")
                        }
                    }
                }
            )

            // Add extra space at the bottom for keyboard
            Spacer(modifier = Modifier.height(240.dp))
        }
    }
}

/**
 * Screen for editing bio
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditBioScreen(
    settingsViewModel: SettingsViewModel,
    navController: NavController
) {
    val uiState by settingsViewModel.uiState.collectAsState()
    val currentUserBio = uiState.user.bio
    val isLoading = uiState.isLoading

    var isBioEditable by remember { mutableStateOf(false) }
    var tempBio by remember(currentUserBio, settingsViewModel.tempBio, isBioEditable) {
        mutableStateOf(if (isBioEditable) settingsViewModel.tempBio else currentUserBio)
    }
    val maxBioLength = 100
    val scrollState = rememberScrollState()

    // Update ViewModel's tempBio whenever our local tempBio changes *while in edit mode*
    LaunchedEffect(tempBio, isBioEditable) {
        if (isBioEditable) {
            settingsViewModel.tempBio = tempBio
        }
    }

    // Reset edit mode on disposal
    DisposableEffect(Unit) {
        onDispose {
            isBioEditable = false
            // settingsViewModel.tempBio = uiState.user.bio // ViewModel handles its temp state
        }
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Edit Bio") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }, enabled = !isLoading) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                actions = {
                    if (isBioEditable) {
                        IconButton(
                            onClick = {
                                settingsViewModel.saveProfileChanges()
                                isBioEditable = false // Exit edit mode
                                // navController.navigateUp() // Consider if navigation should happen here
                            },
                            enabled = !isLoading
                        ) {
                             if (isLoading) {
                                CircularProgressIndicator(modifier = Modifier.size(24.dp), strokeWidth = 2.dp)
                            } else {
                                Icon(Icons.Default.Check, contentDescription = "Save Bio")
                            }
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(16.dp)
                .imePadding() // Handle keyboard insets
        ) {
            OutlinedTextField(
                value = tempBio,
                onValueChange = {
                    if (it.length <= maxBioLength) tempBio = it
                    if (uiState.errorMessage != null) { // Clear ViewModel error if user types
                        settingsViewModel.clearErrorMessage()
                    }
                },
                label = { Text("Bio") },
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 150.dp),
                enabled = isBioEditable,
                readOnly = !isBioEditable,
                isError = !uiState.errorMessage.isNullOrBlank() && isBioEditable,
                supportingText = {
                    if (!uiState.errorMessage.isNullOrBlank() && isBioEditable) {
                        Text(uiState.errorMessage!!, color = MaterialTheme.colorScheme.error)
                    } else if (!isBioEditable) {
                        Text(if (tempBio.isBlank()) "Tap the edit icon to add a bio" else "Tap the edit icon to change your bio")
                    } else {
                        Text("${tempBio.length}/$maxBioLength")
                    }
                },
                maxLines = 5,
                trailingIcon = {
                    if (!isBioEditable && !isLoading) {
                        IconButton(onClick = {
                            val currentActualBio = settingsViewModel.uiState.value.user.bio
                            settingsViewModel.tempBio = currentActualBio
                            tempBio = currentActualBio
                            isBioEditable = true
                            settingsViewModel.clearErrorMessage() // Clear any previous errors
                        }) {
                            Icon(Icons.Default.Edit, contentDescription = "Edit Bio")
                        }
                    }
                }
            )

            // Add extra space at the bottom for keyboard
            Spacer(modifier = Modifier.height(240.dp))
        }
    }
}

/**
 * Screen for editing email
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditEmailScreen(
    settingsViewModel: SettingsViewModel,
    navController: NavController
) {
    var tempEmail by remember { mutableStateOf(settingsViewModel.tempEmail) }
    val uiState by settingsViewModel.uiState.collectAsState() // Collect the whole state
    val isVerified = uiState.isEmailVerified
    val verificationSent = uiState.isVerificationSent
    val isLoading = uiState.isLoading // Get loading state
    val scrollState = rememberScrollState()
    val context = LocalContext.current // Get context for sign out

    // New state for controlling edit mode
    var isEmailEditable by remember { mutableStateOf(false) }

    // State for controlling the confirmation dialog
    var showVerificationDialog by remember { mutableStateOf(false) }

    // --- Effect to clear error on disposal ---
    DisposableEffect(Unit) {
        onDispose {
            settingsViewModel.clearTemporaryEmailField()
            isEmailEditable = false // Reset edit mode on leaving screen
        }
    }
    // --- End Effect ---

    // Collect the event to show the dialog
    LaunchedEffect(Unit) {
        settingsViewModel.showVerificationSentDialogEvent.collect {
            showVerificationDialog = true
            // isEmailEditable = false // Moved to after save/loading check
        }
    }

    // Effect to hide verification dialog if an error occurs
    LaunchedEffect(uiState.errorMessage) {
        if (!uiState.errorMessage.isNullOrBlank()) {
            showVerificationDialog = false
        }
    }

    // Effect to make email non-editable after successful save/verification sent and loading finishes
    // This observes isLoading and isVerificationSent
    LaunchedEffect(isLoading, verificationSent, uiState.errorMessage) {
        if (!isLoading && verificationSent && uiState.errorMessage.isNullOrBlank()) {
            // If not loading, verification has been sent, and no error, then make field read-only
            isEmailEditable = false
        }
    }

    // --- Verification Sent Dialog ---
    if (showVerificationDialog) {
        AlertDialog(
            onDismissRequest = {
                // Optional: Allow dismissing without signing out?
                // showVerificationDialog = false
                // For now, force OK click to sign out
            },
            title = { Text("Verification Email Sent") },
            text = { Text("Please check your email (including spam folder) to verify your new address. You will be signed out now to complete the process.") },
            confirmButton = {
                TextButton(
                    onClick = {
                        showVerificationDialog = false
                        settingsViewModel.signOut(context) // Sign out and navigate
                    }
                ) {
                    Text("OK")
                }
            }
        )
    }
    // --- End Dialog ---

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Edit Email") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }, enabled = !isLoading) { // Disable if loading
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                actions = {
                    // Save button - only active if in edit mode and not loading
                    if (isEmailEditable) {
                        IconButton(
                            onClick = {
                                settingsViewModel.tempEmail = tempEmail
                                settingsViewModel.saveProfileChanges()
                                // isEmailEditable = false; // Moved to LaunchedEffect based on ViewModel state change
                            },
                            enabled = !isLoading
                        ) {
                            if (isLoading) {
                                 CircularProgressIndicator(
                                     modifier = Modifier.size(24.dp),
                                     strokeWidth = 2.dp
                                 )
                            } else {
                                 Icon(Icons.Default.Check, contentDescription = "Save")
                            }
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(16.dp)
                .imePadding() // Handle keyboard insets
        ) {
            OutlinedTextField(
                value = tempEmail,
                onValueChange = {
                    tempEmail = it
                    if (uiState.errorMessage != null) {
                        settingsViewModel.clearErrorMessage()
                    }
                },
                label = { Text("Email") },
                modifier = Modifier.fillMaxWidth(),
                enabled = isEmailEditable, // Controlled by edit mode state
                readOnly = !isEmailEditable, // Opposite of enabled for clarity
                keyboardOptions = KeyboardOptions(
                    keyboardType = KeyboardType.Email,
                    imeAction = ImeAction.Done
                ),
                singleLine = true,
                isError = !uiState.errorMessage.isNullOrBlank(),
                supportingText = {
                    if (!uiState.errorMessage.isNullOrBlank()) {
                        Text(uiState.errorMessage!!, color = MaterialTheme.colorScheme.error)
                    } else if (isVerified) {
                        Text("Email is verified")
                    } else if (verificationSent && !isEmailEditable) { // Show verification sent only if not editing
                        Text("Verification email sent. Check your inbox.")
                    } else if (isEmailEditable) {
                        Text("Enter your new email address")
                    } else {
                        Text(if (tempEmail.isBlank()) "Tap the edit icon to add an email" else "Tap the edit icon to change your email")
                    }
                },
                trailingIcon = {
                    if (!isEmailEditable) {
                        IconButton(onClick = { isEmailEditable = true }) {
                            Icon(Icons.Default.Edit, contentDescription = "Edit Email")
                        }
                    }
                    // No trailing icon when in edit mode, save is in TopAppBar
                }
            )

            // Conditional Send/Resend button (Keep as is, but maybe hide if isEmailEditable is true and email changed?)
            // This button is for an EXISTING, UNVERIFIED email.
            if (!isVerified && tempEmail.isNotBlank() && tempEmail == uiState.user.email && !isEmailEditable) {
                Spacer(modifier = Modifier.height(16.dp))
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { settingsViewModel.sendVerificationEmail() }
                        .padding(vertical = 12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = if (verificationSent) "Resend Verification Email" else "Send Verification Email",
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Bold
                    )
                }
            }

            Spacer(modifier = Modifier.height(24.dp)) // Space before info section

            // --- Informational Section ---
            Text(
                text = "Add email to protect your account",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            Column(modifier = Modifier.padding(start = 8.dp)) { // Indent the points slightly
                InfoPoint(icon = Icons.Filled.VerifiedUser, text = "Verify your account, even without SMS.")
                Spacer(modifier = Modifier.height(12.dp))
                InfoPoint(icon = Icons.Filled.ChatBubbleOutline, text = "Email helps us reach you in case of security or support issues.")
                Spacer(modifier = Modifier.height(12.dp))
                InfoPoint(icon = Icons.Filled.Lock, text = "Your email address won't be visible to others.")
            }
            // --- End Informational Section ---

            Spacer(modifier = Modifier.height(24.dp)) // Space after info section

            // --- Additional Security/Recovery Text ---
            Text(
                text = "Adding email will add an extra layer of security and ensure you can recover your account. " +
                       "If you ever need to register your phone number, you'll be able to use your verified email " +
                       "to get back into you account by following the steps in the email we send to you.",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant, // Use a secondary text color
                modifier = Modifier.padding(horizontal = 8.dp) // Slight horizontal padding
            )
            // --- End Additional Text ---

            // Add extra space at the bottom for keyboard
            Spacer(modifier = Modifier.height(80.dp)) // Reduced bottom spacer a bit
        }
    }
}

/**
 * Helper composable for displaying an icon and text row in the info section.
 */
@Composable
private fun InfoPoint(icon: androidx.compose.ui.graphics.vector.ImageVector, text: String) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Icon(
            imageVector = icon,
            contentDescription = null, // Decorative icon
            tint = MaterialTheme.colorScheme.primary, // Use primary color for icons
            modifier = Modifier.size(24.dp)
        )
        Spacer(modifier = Modifier.width(16.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodyLarge
        )
    }
}

/**
 * Screen for editing profile photo
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditPhotoScreen(
    settingsViewModel: SettingsViewModel,
    navController: NavController
) {
    // Collect necessary states from the ViewModel
    val isUploading by settingsViewModel.isUploading.collectAsState() // Assuming isUploading is a StateFlow
    val currentPhotoUrl = settingsViewModel.tempPhotoUrl // Assuming this reflects the latest URL attempt
    val uiState by settingsViewModel.uiState.collectAsState()

    // Local state to hold the URI selected by the user
    var selectedImageUri by remember { mutableStateOf<Uri?>(null) }
    val scrollState = rememberScrollState()
    val context = LocalContext.current // Get context for Toast

    // Permission launcher for media access
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (!isGranted) {
            Toast.makeText(context, "Permission required to access photos", Toast.LENGTH_LONG).show()
        }
    }

    // Observe upload completion events from the ViewModel
    LaunchedEffect(Unit) { // Use Unit to launch only once
        settingsViewModel.uploadCompleteEvent.collect {
            // Show a brief confirmation message (optional)
            Toast.makeText(context, "Profile photo updated!", Toast.LENGTH_SHORT).show()
            // Navigate back after successful upload and state update
            navController.navigateUp()
        }
    }

    // Observe upload error events
    LaunchedEffect(Unit) {
        settingsViewModel.uploadErrorEvent.collect { errorMessage ->
            // Show error message to the user
            Toast.makeText(context, "Upload failed: $errorMessage", Toast.LENGTH_LONG).show()
            selectedImageUri = null // Clear selected image on error if desired
        }
    }

    // Function to check and request permissions
    fun checkAndRequestPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ - check for READ_MEDIA_IMAGES
            when (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_MEDIA_IMAGES)) {
                PackageManager.PERMISSION_GRANTED -> true
                else -> {
                    permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES)
                    false
                }
            }
        } else {
            // Android 12 and below - check for READ_EXTERNAL_STORAGE
            when (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_EXTERNAL_STORAGE)) {
                PackageManager.PERMISSION_GRANTED -> true
                else -> {
                    permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE)
                    false
                }
            }
        }
    }

    // Launcher for picking an image from the gallery
    val imagePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent(),
        onResult = { uri: Uri? ->
            if (uri != null) {
                selectedImageUri = uri
                Log.d("EditPhotoScreen", "Image selected: $uri")
                // Trigger upload and update URL in ViewModel
                // ViewModel should handle setting isUploading state
                settingsViewModel.uploadProfileImageAndUpdateUrl(uri)
            } else {
                Log.d("EditPhotoScreen", "No image selected")
            }
        }
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Profile Photo") },
                navigationIcon = {
                    // Back button, always enabled unless uploading
                    IconButton(
                        onClick = { navController.navigateUp() },
                        enabled = !isUploading
                    ) {
                        Icon(
                            Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = if (isUploading) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                  else MaterialTheme.colorScheme.onSurface
                        )
                    }
                },
                actions = {
                    // Done button - primarily for dismissing without changes if no upload pending
                    IconButton(
                        onClick = { navController.navigateUp() },
                        enabled = !isUploading // Disable when uploading
                    ) {
                        Icon(
                            Icons.Default.Check,
                            contentDescription = "Done",
                            tint = if (isUploading) MaterialTheme.colorScheme.onSurface.copy(alpha = 0.38f)
                                  else MaterialTheme.colorScheme.onSurface
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(16.dp))

            // Photo preview with upload state
            Box(contentAlignment = Alignment.Center) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        // Prioritize showing the newly selected URI, fallback to ViewModel's URL
                        .data(selectedImageUri ?: currentPhotoUrl.ifEmpty { null })
                        .crossfade(true)
                        .build(),
                    contentDescription = "Profile Picture Preview",
                    modifier = Modifier
                        .size(200.dp)
                        .clip(CircleShape),
                    contentScale = ContentScale.Crop,
                    // Display placeholder while loading or if URL/URI is invalid
                    placeholder = painterResource(id = R.drawable.ic_launcher_background), // Replace with your actual placeholder drawable
                    error = painterResource(id = R.drawable.ic_launcher_background) // Replace with your actual error drawable or placeholder
                )

                // Show placeholder if no image selected or available
                if (selectedImageUri == null && currentPhotoUrl.isEmpty()) {
                    Box(
                        modifier = Modifier
                            .size(200.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.primaryContainer),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "Default Profile Icon",
                            tint = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.size(100.dp)
                        )
                    }
                }

                // Show loading indicator if uploading
                if (isUploading) {
                    Box(
                        modifier = Modifier
                            .size(200.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.surface.copy(alpha = 0.7f)),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
            }

            // Show error message if there's an upload error
            uiState.errorMessage?.let { errorMessage ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.errorContainer
                    )
                ) {
                    Text(
                        text = errorMessage,
                        modifier = Modifier.padding(16.dp),
                        color = MaterialTheme.colorScheme.onErrorContainer,
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
                Spacer(modifier = Modifier.height(16.dp))
            }

            Spacer(modifier = Modifier.height(32.dp))

            // Button to change picture
            Surface(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(enabled = !isUploading) {
                        if (checkAndRequestPermission()) {
                            imagePickerLauncher.launch("image/*")
                        }
                    }
                    .padding(vertical = 12.dp),
                color = MaterialTheme.colorScheme.surface // Use surface color for the clickable area
            ) {
                Row( // Use Row for better alignment
                    modifier = Modifier
                        .fillMaxWidth() // Ensure Row takes full width
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center // Center content horizontally
                ) {
                    Icon( // Add an icon for better UI (optional)
                        imageVector = Icons.Default.Edit,
                        contentDescription = null, // Decorative
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(20.dp)
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = "Choose from gallery",
                        // Removed modifier padding here as it's handled by parent Row/Surface
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.SemiBold
                    )
                }
            }

            // Add extra space at the bottom
            Spacer(modifier = Modifier.height(80.dp))
        }
    }
}

/**
 * Reusable placeholder screen for sections under development
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PlaceholderScreen(
    title: String,
    navController: NavController
) {
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(title) },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "This feature is coming soon!",
                style = MaterialTheme.typography.headlineMedium
            )
        }
    }
}

/**
 * Composable for displaying a poll option with progress bar
 */
@Composable
fun PollOptionItem(
    option: String,
    percentage: Int
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = option,
                style = MaterialTheme.typography.bodyLarge
            )

            Text(
                text = "$percentage%",
                color = MaterialTheme.colorScheme.primary,
                style = MaterialTheme.typography.bodyMedium
            )
        }

        Spacer(modifier = Modifier.height(4.dp))

        // Progress bar
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(8.dp)
                .background(
                    color = MaterialTheme.colorScheme.surfaceVariant,
                    shape = RoundedCornerShape(4.dp)
                )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(percentage / 100f)
                    .height(8.dp)
                    .background(
                        color = MaterialTheme.colorScheme.primary,
                        shape = RoundedCornerShape(4.dp)
                    )
            )
        }
    }
}

/**
 * Composable for displaying a past decision card
 */
@Composable
fun PastDecisionCard(
    title: String,
    description: String,
    decision: String,
    votes: Int
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Poll title
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            // Poll description
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(vertical = 4.dp)
            )

            Spacer(modifier = Modifier.height(4.dp))

            // Final decision
            Text(
                text = "Final Decision: $decision",
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.SemiBold
            )

            // Vote count
            Text(
                text = "$votes votes",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier
                    .align(Alignment.End)
                    .padding(top = 8.dp)
            )
        }
    }
}

/**
 * Screen for managing notification preferences
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationPreferencesScreen(
    settingsViewModel: SettingsViewModel,
    navController: NavController
) {
    val uiState by settingsViewModel.uiState.collectAsState()
    val notificationSettings = uiState.user.notificationSettings
    val isLoading = uiState.isLoading
    val scrollState = rememberScrollState()

    // State for toggles
    var pollInvitations by remember { mutableStateOf(notificationSettings.pollInvitations) }
    var voteNotifications by remember { mutableStateOf(notificationSettings.voteNotifications) }
    var pollResults by remember { mutableStateOf(notificationSettings.pollResults) }
    var commentNotifications by remember { mutableStateOf(notificationSettings.commentNotifications) }
    var appUpdates by remember { mutableStateOf(notificationSettings.appUpdates) }
    var notifyOnFriendRequest by remember { mutableStateOf(notificationSettings.notifyOnFriendRequest) } // New state

    // Track last saved values to avoid unnecessary saves
    var lastSavedPollInvitations by remember { mutableStateOf(pollInvitations) }
    var lastSavedVoteNotifications by remember { mutableStateOf(voteNotifications) }
    var lastSavedPollResults by remember { mutableStateOf(pollResults) }
    var lastSavedCommentNotifications by remember { mutableStateOf(commentNotifications) }
    var lastSavedAppUpdates by remember { mutableStateOf(appUpdates) }
    var lastSavedNotifyOnFriendRequest by remember { mutableStateOf(notifyOnFriendRequest) } // New saved state tracker

    // Save changes function
    val saveChanges = {
        if (pollInvitations != lastSavedPollInvitations ||
            voteNotifications != lastSavedVoteNotifications ||
            pollResults != lastSavedPollResults ||
            commentNotifications != lastSavedCommentNotifications ||
            appUpdates != lastSavedAppUpdates ||
            notifyOnFriendRequest != lastSavedNotifyOnFriendRequest) { // Condition for new toggle

            settingsViewModel.saveNotificationPreferences(
                pollInvitations = pollInvitations,
                voteNotifications = voteNotifications,
                pollResults = pollResults,
                commentNotifications = commentNotifications,
                appUpdates = appUpdates,
                notifyOnFriendRequest = notifyOnFriendRequest // Pass new value
            )

            lastSavedPollInvitations = pollInvitations
            lastSavedVoteNotifications = voteNotifications
            lastSavedPollResults = pollResults
            lastSavedCommentNotifications = commentNotifications
            lastSavedAppUpdates = appUpdates
            lastSavedNotifyOnFriendRequest = notifyOnFriendRequest // Update saved state tracker
        }
    }

    // Effect to save changes when values change
    LaunchedEffect(pollInvitations, voteNotifications, pollResults, commentNotifications, appUpdates, notifyOnFriendRequest) { // Add to LaunchedEffect keys
        // Save after a small delay to avoid saving during rapid changes
        delay(300)
        saveChanges()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Notification Preferences") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
                .padding(bottom = 100.dp) // Add extra padding at the bottom for scrolling
        ) {
            // Header section with bell icon
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Notifications,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(36.dp)
                )
                Text(
                    text = "Notification Preferences",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Bold
                    ),
                    modifier = Modifier.padding(start = 16.dp)
                )

                // Small loading indicator that doesn't block interaction
                if (isLoading) {
                    Spacer(modifier = Modifier.weight(1f))
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                }
            }

            HorizontalDivider()

            // Poll Invitations
            NotificationPreferenceItem(
                title = "Poll Invitations",
                description = "Receive notifications when you're invited to a poll",
                checked = pollInvitations,
                onCheckedChange = { pollInvitations = it }
            )

            // Vote Notifications
            NotificationPreferenceItem(
                title = "Vote Notifications",
                description = "Receive notifications when someone votes on your poll",
                checked = voteNotifications,
                onCheckedChange = { voteNotifications = it }
            )

            // Poll Results
            NotificationPreferenceItem(
                title = "Poll Results",
                description = "Receive notifications when a poll you participated in ends",
                checked = pollResults,
                onCheckedChange = { pollResults = it }
            )

            // Comment Notifications
            NotificationPreferenceItem(
                title = "Comment Notifications",
                description = "Receive notifications for new comments on your polls",
                checked = commentNotifications,
                onCheckedChange = { commentNotifications = it }
            )

            // App Updates
            NotificationPreferenceItem(
                title = "App Updates",
                description = "Receive notifications about new app features and updates",
                checked = appUpdates,
                onCheckedChange = { appUpdates = it }
            )

            // Friend Requests Toggle (New Item)
            NotificationPreferenceItem(
                title = "Friend Requests",
                description = "Receive notifications when someone sends you a friend request.",
                checked = notifyOnFriendRequest,
                onCheckedChange = { notifyOnFriendRequest = it }
            )

            // Add extra space at the bottom for scrolling
            Spacer(modifier = Modifier.height(40.dp))
        }
    }
}

/**
 * Composable for a single notification preference item with a toggle
 */
@Composable
fun NotificationPreferenceItem(
    title: String,
    description: String,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(end = 16.dp) // Add more padding to avoid text getting too close to switch
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }

        // Use custom colors for switch to have border on track
        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange,
            colors = SwitchDefaults.colors(
                // White thumb for contrast
                checkedThumbColor = Color.White,
                uncheckedThumbColor = Color.White,
                // Colored tracks with darker borders
                checkedTrackColor = Color.Green.copy(alpha = 0.7f),
                uncheckedTrackColor = Color.Red.copy(alpha = 0.5f),
                // Border colors for tracks
                checkedBorderColor = Color.Black.copy(alpha = 0.7f),
                uncheckedBorderColor = Color.Black.copy(alpha = 0.7f)
            )
        )
    }

    HorizontalDivider()
}

/**
 * Screen for privacy settings
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PrivacySettingsScreen(
    settingsViewModel: SettingsViewModel,
    navController: NavController
) {
    val uiState by settingsViewModel.uiState.collectAsState()
    val user = uiState.user
    val isLoading = uiState.isLoading
    val scrollState = rememberScrollState()

    // State for privacy settings
    var publicProfile by remember { mutableStateOf(user.publicProfile) }
    var hideOnlineStatus by remember { mutableStateOf(user.hideOnlineStatus) }
    var allowTagging by remember { mutableStateOf(user.allowTagging) }
    val profileVisibility by remember { mutableStateOf(user.profileVisibility) }

    // Track last saved values to avoid unnecessary saves
    var lastSavedPublicProfile by remember { mutableStateOf(publicProfile) }
    var lastSavedHideOnlineStatus by remember { mutableStateOf(hideOnlineStatus) }
    var lastSavedAllowTagging by remember { mutableStateOf(allowTagging) }

    // Save changes function
    val saveChanges = {
        if (publicProfile != lastSavedPublicProfile ||
            hideOnlineStatus != lastSavedHideOnlineStatus ||
            allowTagging != lastSavedAllowTagging) {

            settingsViewModel.savePrivacySettings(
                allowTagging = allowTagging,
                profileVisibility = profileVisibility,
                hideOnlineStatus = hideOnlineStatus,
                publicProfile = publicProfile
            )

            lastSavedPublicProfile = publicProfile
            lastSavedHideOnlineStatus = hideOnlineStatus
            lastSavedAllowTagging = allowTagging
        }
    }

    // Effect to save changes when values change
    LaunchedEffect(publicProfile, hideOnlineStatus, allowTagging) {
        // Save after a small delay to avoid saving during rapid changes
        delay(300)
        saveChanges()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Privacy Settings") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
                .padding(bottom = 100.dp) // Add extra padding at the bottom for scrolling
        ) {
            // Header with lock icon
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Lock,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(36.dp)
                )
                Text(
                    text = "Privacy Settings",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Bold
                    ),
                    modifier = Modifier.padding(start = 16.dp)
                )

                // Small loading indicator that doesn't block interaction
                if (isLoading) {
                    Spacer(modifier = Modifier.weight(1f))
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                }
            }

            HorizontalDivider()

            // Public Profile Toggle
            NotificationPreferenceItem(
                title = "Public Profile",
                description = "Allow others to see your profile information",
                checked = publicProfile,
                onCheckedChange = { publicProfile = it }
            )

            // Visibility Toggle
            NotificationPreferenceItem(
                title = "Visibility",
                description = "Turn ON to hide your online status from others (OFF means visible)",
                checked = hideOnlineStatus,
                onCheckedChange = { hideOnlineStatus = it }
            )

            // Allow Tagging Toggle
            NotificationPreferenceItem(
                title = "Allow Tagging",
                description = "Allow others to tag you in polls",
                checked = allowTagging,
                onCheckedChange = { allowTagging = it }
            )

            // Data Privacy section
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable { navController.navigate("data_privacy") }
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 16.dp)
                ) {
                    Text(
                        text = "Data Privacy",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(4.dp))

                    Text(
                        text = "View our privacy policy and how we handle your data",
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }

                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "View privacy policy",
                    tint = MaterialTheme.colorScheme.primary
                )
            }

            HorizontalDivider()

            // Add spacer at the bottom
            Spacer(modifier = Modifier.height(40.dp))
        }
    }
}

/**
 * Screen for appearance settings
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppearanceSettingsScreen(
    settingsViewModel: SettingsViewModel,
    navController: NavController
) {
    val scrollState = rememberScrollState()
    val uiState by settingsViewModel.uiState.collectAsState()
    val user = uiState.user
    val isLoading = uiState.isLoading

    // State for appearance settings
    var darkMode by remember { mutableStateOf(user.themePreferences.darkMode) }
    var selectedThemeColorIndex by remember { mutableIntStateOf(user.themePreferences.themeColorIndex) }

    // Track last saved values to avoid unnecessary saves
    var lastSavedDarkMode by remember { mutableStateOf(darkMode) }
    var lastSavedColorIndex by remember { mutableIntStateOf(selectedThemeColorIndex) }

    // Save changes function
    val saveChanges = {
        if (darkMode != lastSavedDarkMode || selectedThemeColorIndex != lastSavedColorIndex) {
            settingsViewModel.saveThemePreferences(
                darkMode = darkMode,
                themeColorIndex = selectedThemeColorIndex
            )
            lastSavedDarkMode = darkMode
            lastSavedColorIndex = selectedThemeColorIndex
        }
    }

    // Effect to update ThemeManager when settings change
    LaunchedEffect(darkMode, selectedThemeColorIndex) {
        // Apply theme change immediately to UI
        ThemeManager.setDarkMode(darkMode)
        ThemeManager.setAccentColor(selectedThemeColorIndex)

        // Save after a small delay to avoid saving during rapid changes
        delay(300)
        saveChanges()
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Appearance") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            // Header with palette icon
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    imageVector = Icons.Default.Palette,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(36.dp)
                )
                Text(
                    text = "Appearance",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Bold
                    ),
                    modifier = Modifier.padding(start = 16.dp)
                )

                // Small loading indicator that doesn't block interaction
                if (isLoading) {
                    Spacer(modifier = Modifier.weight(1f))
                    CircularProgressIndicator(
                        modifier = Modifier.size(24.dp),
                        strokeWidth = 2.dp
                    )
                }
            }

            HorizontalDivider()

            // Dark/Light mode toggle
            NotificationPreferenceItem(
                title = "Dark Mode",
                description = "Enable dark theme throughout the app",
                checked = darkMode,
                onCheckedChange = {
                    darkMode = it
                }
            )

            // Theme color selection
            Text(
                text = "Accent Color",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )

            Text(
                text = "Select your preferred accent color for the app:",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Color selection grid
            AccentColorGrid(
                selectedIndex = selectedThemeColorIndex,
                onColorSelected = { selectedThemeColorIndex = it }
            )

            Spacer(modifier = Modifier.height(32.dp))

            // Restore Defaults button
            val defaultDarkMode = false // Default is light mode
            val defaultColorIndex = 0 // Default is the first color (Blue)

            Button(
                onClick = {
                    darkMode = defaultDarkMode
                    selectedThemeColorIndex = defaultColorIndex

                    // Update ThemeManager immediately to reflect changes
                    ThemeManager.setDarkMode(darkMode)
                    ThemeManager.setAccentColor(selectedThemeColorIndex)

                    // Save changes immediately
                    saveChanges()
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary, // Was primaryContainer
                    contentColor = MaterialTheme.colorScheme.onPrimary // Was onPrimaryContainer
                )
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Check,
                        contentDescription = null,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    Text(
                        text = "Restore Default Settings",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            // Add extra padding at bottom
            Spacer(modifier = Modifier.height(100.dp))
        }
    }
}

/**
 * Grid of accent color options for selection
 */
@Composable
fun AccentColorGrid(
    selectedIndex: Int,
    onColorSelected: (Int) -> Unit
) {
    val colors = ThemeManager.accentColorOptions

    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = 120.dp, max = 240.dp)
            .padding(8.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        itemsIndexed(colors) { index, colorOption ->
            AccentColorItem(
                name = colorOption.name,
                color = colorOption.color,
                isSelected = index == selectedIndex,
                onSelect = { onColorSelected(index) }
            )
        }
    }
}

// Helper function to determine if a color is light or dark
// This can be used to decide the color of content (like a checkmark) on top of this color
private fun isColorLight(color: Color): Boolean {
    val red = color.red * 255
    val green = color.green * 255
    val blue = color.blue * 255
    // Formula for perceived brightness (YIQ formula)
    val perceivedBrightness = ((red * 299) + (green * 587) + (blue * 114)) / 1000
    return perceivedBrightness > 128 // Threshold for light/dark, adjust as needed
}

/**
 * Individual color option item in the color selection grid
 */
@Composable
fun AccentColorItem(
    name: String,
    color: Color,
    isSelected: Boolean,
    onSelect: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable(onClick = onSelect)
            .padding(8.dp)
    ) {
        // Color circle with selection indicator
        Box(
            modifier = Modifier
                .size(48.dp)
                .clip(CircleShape)
                .background(color)
                .border(
                    width = if (isSelected) 2.dp else 1.dp, // Slightly adjust border if needed
                    color = if (isSelected) MaterialTheme.colorScheme.outline else MaterialTheme.colorScheme.outlineVariant, // Use outline for better visibility
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center // Center the checkmark
        ) {
            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "Selected",
                    tint = if (isColorLight(color)) Color.Black else Color.White, // Contrasting checkmark
                    modifier = Modifier.size(28.dp)
                )
            }
        }

        Spacer(modifier = Modifier.height(4.dp))

        // Color name
        Text(
            text = name,
            style = MaterialTheme.typography.bodySmall,
            fontWeight = if (isSelected) FontWeight.Bold else FontWeight.Normal,
            modifier = Modifier.padding(top = 4.dp)
        )
    }
}

/**
 * Screen for account settings
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AccountSettingsScreen(
    settingsViewModel: SettingsViewModel,
    navController: NavController
) {
    val scrollState = rememberScrollState()
    val context = LocalContext.current
    val state = settingsViewModel.uiState.collectAsState().value

    // State for confirmation dialogs
    var showSignOutDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }

    // Show error message if present
    LaunchedEffect(state.errorMessage) {
        state.errorMessage?.let { error ->
            Toast.makeText(context, error, Toast.LENGTH_LONG).show()
            settingsViewModel.clearErrorMessage()
        }
    }

    // Confirmation dialog for sign out
    if (showSignOutDialog) {
        AlertDialog(
            onDismissRequest = { showSignOutDialog = false },
            title = { Text("Sign Out") },
            text = { Text("Are you sure you want to sign out?") },
            confirmButton = {
                Button(
                    onClick = {
                        showSignOutDialog = false
                        settingsViewModel.signOut(context)
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Text("Sign Out")
                }
            },
            dismissButton = {
                TextButton(onClick = { showSignOutDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }

    // Confirmation dialog for delete account
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = { Text("Delete Account") },
            text = {
                Column {
                    Text(
                        "Are you sure you want to permanently delete your account? This action cannot be undone.",
                        style = MaterialTheme.typography.bodyMedium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        "All your data, including polls, votes, and profile information will be permanently removed.",
                        style = MaterialTheme.typography.bodyMedium
                    )
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        showDeleteDialog = false
                        settingsViewModel.deleteAccount(context)
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Text("Delete")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDeleteDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Account") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                    titleContentColor = MaterialTheme.colorScheme.onPrimary,
                    navigationIconContentColor = MaterialTheme.colorScheme.onPrimary
                )
            )
        }
    ) { paddingValues ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(scrollState)
                    .padding(horizontal = 16.dp)
            ) {
                // Header with account icon
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.AccountCircle,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.primary,
                        modifier = Modifier.size(36.dp)
                    )
                    Text(
                        text = "Account",
                        style = MaterialTheme.typography.headlineMedium.copy(
                            color = MaterialTheme.colorScheme.primary,
                            fontWeight = FontWeight.Bold
                        ),
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }

                HorizontalDivider()

                Spacer(modifier = Modifier.height(32.dp))

                // Sign Out Button
                Button(
                    onClick = { showSignOutDialog = true },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary
                    )
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ExitToApp,
                        contentDescription = null,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    Text("Sign Out", style = MaterialTheme.typography.bodyLarge)
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Delete Account Button
                Button(
                    onClick = { showDeleteDialog = true },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = null,
                        modifier = Modifier.padding(end = 8.dp)
                    )
                    Text("Delete Account", style = MaterialTheme.typography.bodyLarge)
                }
            }

            // Loading indicator
            if (state.isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.Black.copy(alpha = 0.4f)),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator(
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }
        }
    }
}

/**
 * Screen displaying the app's privacy policy
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DataPrivacyScreen(
    navController: NavController
) {
    val scrollState = rememberScrollState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("Data Privacy") },
                navigationIcon = {
                    IconButton(onClick = { navController.navigateUp() }) {
                        Icon(Icons.AutoMirrored.Filled.ArrowBack, contentDescription = "Back")
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
                .padding(bottom = 100.dp) // Add extra padding at the bottom for scrolling
        ) {
            // Header
            Text(
                text = "Privacy Policy",
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(vertical = 16.dp)
            )

            HorizontalDivider()

            Text(
                text = "Last Updated: May 15, 2025",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                modifier = Modifier.padding(vertical = 8.dp)
            )

            // Introduction section
            Text(
                text = "Introduction",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )

            Text(
                text = "Can't Decide respects your privacy and is committed to protecting your personal data. This privacy policy explains how we collect, use, and safeguard your information when you use our app.",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Data collection section
            Text(
                text = "Information We Collect",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )

            Text(
                text = "We collect the following information:",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // List of data collected
            val dataPoints = listOf(
                "Phone number (for authentication)",
                "Profile information you provide (display name, bio, profile picture)",
                "Poll data and voting information",
                "Device information for app functionality"
            )

            dataPoints.forEach { point ->
                Row(
                    modifier = Modifier.padding(start = 16.dp, bottom = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(MaterialTheme.colorScheme.primary, CircleShape)
                    )
                    Text(
                        text = point,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }

            // How we use data section
            Text(
                text = "How We Use Your Information",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )

            Text(
                text = "We use your information to:",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // List of how data is used
            val usagePoints = listOf(
                "Provide, maintain, and improve our services",
                "Process and complete poll transactions",
                "Send notifications about poll activity",
                "Respond to comments, questions, and requests",
                "Monitor and analyze trends, usage, and activities"
            )

            usagePoints.forEach { point ->
                Row(
                    modifier = Modifier.padding(start = 16.dp, bottom = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Box(
                        modifier = Modifier
                            .size(6.dp)
                            .background(MaterialTheme.colorScheme.primary, CircleShape)
                    )
                    Text(
                        text = point,
                        style = MaterialTheme.typography.bodyMedium,
                        modifier = Modifier.padding(start = 8.dp)
                    )
                }
            }

            // Your rights section
            Text(
                text = "Your Rights",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )

            Text(
                text = "You have the right to access, update, or delete your information through the app settings. You can also control your privacy preferences through the Privacy Settings screen.",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            // Contact section
            Text(
                text = "Contact Us",
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold,
                modifier = Modifier.padding(top = 16.dp, bottom = 8.dp)
            )

            Text(
                text = "If you have any questions about this privacy policy or our data practices, please contact <NAME_EMAIL>",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(bottom = 32.dp)
            )
        }
    }
}

// Main preview for the profile fragment replacement
@Preview(showBackground = true)
@Composable
fun SettingsFragmentPreview() {
    // For previews, we can't use Hilt, so we'll just show a placeholder
    AppTheme {
        Text("Settings Preview - Not available in preview mode")
    }
}

// Basic Preview
@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    // Apply the AppTheme correctly
    AppTheme {
        // For previews, we can't use Hilt, so we'll just show a placeholder
        Text("Settings Preview - Not available in preview mode")
    }
}
