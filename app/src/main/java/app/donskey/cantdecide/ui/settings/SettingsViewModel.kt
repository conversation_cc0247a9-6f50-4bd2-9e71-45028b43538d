package app.donskey.cantdecide.ui.settings

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.util.Log
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import app.donskey.cantdecide.data.models.User
import app.donskey.cantdecide.data.repositories.UserRepository
import app.donskey.cantdecide.firebase.FirebaseHelper
import app.donskey.cantdecide.ui.auth.LoginActivity
import com.google.firebase.storage.FirebaseStorage
import com.google.firebase.storage.ktx.storage
import com.google.firebase.ktx.Firebase
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import com.google.firebase.auth.FirebaseAuthRecentLoginRequiredException
import com.google.firebase.auth.FirebaseAuthUserCollisionException
import com.google.firebase.firestore.FieldValue
import kotlinx.coroutines.Job

/**
 * Data class representing the state of the Settings screen.
 */
data class SettingsUiState(
    val user: User = User(), // Holds the user profile data
    val isLoading: Boolean = false, // Indicates if data is loading
    val errorMessage: String? = null, // Holds error messages
    val isUploading: Boolean = false, // Add uploading state
    val isEmailVerified: Boolean = false, // Added to track email verification status
    val isVerificationSent: Boolean = false // Added to track if verification email has been sent
)

/**
 * ViewModel for the Settings screen.
 * Manages the UI state and interacts with the UserRepository.
 */
@HiltViewModel
class SettingsViewModel @Inject constructor(
    private val userRepository: UserRepository,
    private val auth: FirebaseAuth,
    private val firestore: FirebaseFirestore
) : ViewModel() {

    companion object {
        private const val TAG = "SettingsViewModel"
        private const val PROFILE_IMAGES_PATH = "profile_images"
    }

    // Job for observing user profile updates
    private var userProfileObservationJob: Job? = null

    // Get Storage instance explicitly with bucket URL
    private val storage: FirebaseStorage = Firebase.storage("gs://cant-decide-app.firebasestorage.app")

    // Mutable state flow for the UI state
    private val _uiState = MutableStateFlow(SettingsUiState())
    // Expose the state flow as immutable StateFlow
    val uiState: StateFlow<SettingsUiState> = _uiState.asStateFlow()

    // Event Flows for Upload status
    private val _uploadCompleteEvent = MutableSharedFlow<Unit>()
    val uploadCompleteEvent = _uploadCompleteEvent.asSharedFlow()

    private val _uploadErrorEvent = MutableSharedFlow<String>()
    val uploadErrorEvent = _uploadErrorEvent.asSharedFlow()

    // Event flow for showing the verification sent dialog
    private val _showVerificationSentDialogEvent = MutableSharedFlow<Unit>()
    val showVerificationSentDialogEvent = _showVerificationSentDialogEvent.asSharedFlow()

    // Event flow for showing critical errors on the main Settings Screen as a dialog
    private val _showSettingsScreenErrorEvent = MutableSharedFlow<String>()
    val showSettingsScreenErrorEvent = _showSettingsScreenErrorEvent.asSharedFlow()

    // Edit mode state
    private var isEditMode by mutableStateOf(false)

    // Temporary state for editing
    var tempDisplayName by mutableStateOf("")
    var tempPhotoUrl by mutableStateOf("") // Holds the *current* URL being edited/previewed
    var tempBio by mutableStateOf("") // Holds the bio while editing
    var tempEmail by mutableStateOf("") // Added to hold temporary email value while editing

    // Publicly readable upload status - uses state flow now
    val isUploading: StateFlow<Boolean> = MutableStateFlow(_uiState.value.isUploading)
        .also { flow ->
             viewModelScope.launch {
                 _uiState.collect { state -> flow.value = state.isUploading }
             }
        }

    // Publicly readable verification status
    val isEmailVerified: Boolean
        get() = _uiState.value.isEmailVerified

    val isVerificationSent: Boolean
        get() = _uiState.value.isVerificationSent

    // Initialize ViewModel: Fetch user profile
    init {
        // Remove direct call to fetchUserProfile()
        // Start observing the user profile in real-time
        observeUserProfileUpdates()
        // Check email verification status
        checkEmailVerificationStatus()
        // Debug Firebase Storage configuration
        debugFirebaseStorageConfig()
    }

    /**
     * Debug function to log Firebase Storage configuration
     */
    private fun debugFirebaseStorageConfig() {
        try {
            Log.d(TAG, "Firebase Storage Debug Info:")
            Log.d(TAG, "Storage bucket: ${storage.reference.bucket}")
            Log.d(TAG, "Storage reference: ${storage.reference}")
            Log.d(TAG, "Auth current user: ${auth.currentUser?.uid}")
            Log.d(TAG, "Profile images path: $PROFILE_IMAGES_PATH")
        } catch (e: Exception) {
            Log.e(TAG, "Error getting storage debug info", e)
        }
    }

    /**
     * Observes user profile updates from the repository and updates the UI state.
     */
    private fun observeUserProfileUpdates() {
        _uiState.update { it.copy(isLoading = true, errorMessage = null) } // Initial loading state

        // Cancel any existing job before starting a new one
        userProfileObservationJob?.cancel()
        userProfileObservationJob = viewModelScope.launch {
            userRepository.observeUserProfile().collect { userProfile ->
                if (userProfile != null) {
                    Log.d(TAG, "User profile observed: DisplayName='${userProfile.displayName}', PhotoUrl='${userProfile.photoUrl}', Friends: ${userProfile.friendsCount}")
                    _uiState.update {
                        it.copy(user = userProfile, isLoading = false, errorMessage = null)
                    }
                    // Initialize/update temp fields when profile is observed
                    // This ensures edit fields are also up-to-date if the profile changes while editing (though ideally editing would pause observation or handle conflicts)
                    tempDisplayName = userProfile.displayName
                    tempPhotoUrl = userProfile.photoUrl
                    tempBio = userProfile.bio
                    tempEmail = userProfile.email
                } else {
                    // Handle case where profile is not found or an error occurred in the flow
                    Log.w(TAG, "Observed null user profile or error in flow.")
                    _uiState.update {
                        // Decide if we show an error or just a non-loaded state
                        // For a continuous flow, an error might mean the listener failed permanently.
                        // Or it could mean the user doc was deleted.
                        it.copy(isLoading = false, errorMessage = it.errorMessage ?: "User profile not available.")
                    }
                }
            }
            // Note: If observeUserProfile() flow completes or errors out in a way that stops collection,
            // isLoading might remain true if not handled inside the collect block for terminal events.
            // However, a well-behaved snapshot listener flow should not complete unless the scope is cancelled.
        }
    }

    /**
     * Checks if the current Firebase user's email is verified.
     * Also attempts to clean up any pending verification record if verification is successful.
     */
    private fun checkEmailVerificationStatus() {
        viewModelScope.launch {
            val currentUser = auth.currentUser
            if (currentUser == null) {
                 Log.w(TAG, "checkEmailVerificationStatus: No authenticated user.")
                 // Ensure state reflects logged-out status if necessary
                 // _uiState.update { it.copy(isEmailVerified = false, isVerificationSent = false) }
                 return@launch
            }

            try {
                // First ensure we have the latest user data
                Log.d(TAG, "checkEmailVerificationStatus: Reloading user ${currentUser.uid}...")
                currentUser.reload().await()
                Log.d(TAG, "checkEmailVerificationStatus: Reload complete.")

                // Get the latest verification status
                val isVerified = currentUser.isEmailVerified
                val verifiedEmail = currentUser.email // Get the email associated with the auth user

                Log.d(TAG, "checkEmailVerificationStatus: User ${currentUser.uid} email verification status: $isVerified, Email: $verifiedEmail")

                // Update the UI state with the latest verification status
                // Do this *before* attempting cleanup
                _uiState.update { it.copy(isEmailVerified = isVerified) }

                // If verified AND email exists, try to clean up the pending record
                if (isVerified && !verifiedEmail.isNullOrBlank()) {
                    val normalizedEmail = verifiedEmail.lowercase()
                    Log.d(TAG, "checkEmailVerificationStatus: Email is verified. Attempting to delete pending record for $normalizedEmail")
                    val pendingEmailRef = firestore.collection("pendingEmailVerifications").document(normalizedEmail)

                    try {
                        // Check if the document actually exists before deleting
                        val doc = pendingEmailRef.get().await()
                        if (doc.exists()) {
                             // Ensure the pending record belongs to this user before deleting
                             val pendingUserId = doc.getString("userId")
                             if (pendingUserId == currentUser.uid) {
                                 pendingEmailRef.delete().await()
                                 Log.d(TAG, "checkEmailVerificationStatus: Successfully deleted pending verification record for $normalizedEmail.")
                             } else {
                                  Log.w(TAG, "checkEmailVerificationStatus: Pending record for $normalizedEmail exists but belongs to different user ($pendingUserId). Skipping delete.")
                             }
                        } else {
                             Log.d(TAG, "checkEmailVerificationStatus: No pending verification record found for $normalizedEmail (already cleaned up or never existed).")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "checkEmailVerificationStatus: Failed to delete pending verification record for $normalizedEmail", e)
                        // Don't necessarily show error to user, just log it. Cleanup failure isn't critical path.
                    }
                }

            } catch (e: Exception) {
                Log.e(TAG, "checkEmailVerificationStatus: Error reloading user or checking status", e)
                // Optionally update UI state with an error message if reload fails critically
                // _uiState.update { it.copy(errorMessage = "Error checking email status: ${e.message}") }
            }
        }
    }

    /**
     * Sends a verification email to the user's email address.
     */
    fun sendVerificationEmail() {
        viewModelScope.launch {
            _uiState.update { it.copy(errorMessage = null) } // Clear previous errors
            val currentUser = auth.currentUser
            if (currentUser == null) {
                Log.w(TAG, "sendVerificationEmail: User not logged in")
                _uiState.update { it.copy(errorMessage = "Cannot send verification email: User not logged in") }
                return@launch
            }

            try {
                // Reload to ensure latest state
                Log.d(TAG, "sendVerificationEmail: Reloading user data...")
                currentUser.reload().await()
                Log.d(TAG, "sendVerificationEmail: User data reloaded. Email verified status: ${currentUser.isEmailVerified}")

                // Check if email is already verified
                if (currentUser.isEmailVerified) {
                    Log.d(TAG, "sendVerificationEmail: Email (${currentUser.email}) is already verified.")
                    _uiState.update { it.copy(isEmailVerified = true, isVerificationSent = false) }
                    return@launch
                }

                // Determine email to verify
                // Use tempEmail if in edit mode, otherwise use the email from the current user state
                val emailToVerify = if (isEditMode) tempEmail else _uiState.value.user.email
                Log.d(TAG, "sendVerificationEmail: Determined email to verify: $emailToVerify (isEditMode: $isEditMode)")

                // Check if we have a valid email to verify
                if (emailToVerify.isBlank()) {
                    Log.e(TAG, "sendVerificationEmail: No email address found for verification.")
                    _uiState.update {
                        it.copy(errorMessage = "No email address found. Please add an email address to your profile first.")
                    }
                    return@launch
                }

                // Check if the email to verify differs from the currently authenticated user's email
                if (emailToVerify != currentUser.email) {
                    // Email is different: Use verifyBeforeUpdateEmail
                    Log.d(TAG, "sendVerificationEmail: Email ($emailToVerify) differs from Auth email (${currentUser.email}). Calling verifyBeforeUpdateEmail...")
                    try {
                        currentUser.verifyBeforeUpdateEmail(emailToVerify).await()
                        Log.d(TAG, "sendVerificationEmail: SUCCESS - verifyBeforeUpdateEmail completed for $emailToVerify")
                        // Update state to show verification is sent and email is not yet verified
                        _uiState.update { it.copy(isVerificationSent = true, isEmailVerified = false) }
                        // Emit event to show the sign-out dialog
                        _showVerificationSentDialogEvent.emit(Unit)
                    } catch (e: FirebaseAuthRecentLoginRequiredException) {
                        Log.w(TAG, "sendVerificationEmail: verifyBeforeUpdateEmail FAILED - Requires recent login.", e)
                        _uiState.update {
                            it.copy(errorMessage = "Changing email requires recent login. Please sign out and sign back in to update your email.")
                        }
                        return@launch // Stop the process
                    } catch (e: Exception) {
                        Log.e(TAG, "sendVerificationEmail: verifyBeforeUpdateEmail FAILED for $emailToVerify", e)
                        _uiState.update {
                            it.copy(errorMessage = "Failed to update and verify email: ${e.message}", isVerificationSent = false)
                        }
                        return@launch // Stop if update fails
                    }
                } else {
                    // Email is the same as auth email: Use sendEmailVerification
                    // This handles the case where the user clicks the "Verify" link for an existing, unverified email.
                    Log.d(TAG, "sendVerificationEmail: Email ($emailToVerify) is same as Auth email. Calling sendEmailVerification...")
                    try {
                        currentUser.sendEmailVerification().await()
                        Log.d(TAG, "sendVerificationEmail: SUCCESS - sendEmailVerification completed for ${currentUser.email}")
                        // Update state to show verification is sent
                        _uiState.update { it.copy(isVerificationSent = true, isEmailVerified = false) }
                        // Emit event to show the sign-out dialog
                        _showVerificationSentDialogEvent.emit(Unit)
                    } catch (e: Exception) {
                         Log.e(TAG, "sendVerificationEmail: sendEmailVerification FAILED for ${currentUser.email}", e)
                         _uiState.update {
                            it.copy(errorMessage = "Failed to send verification email: ${e.message}", isVerificationSent = false)
                        }
                        return@launch // Stop if sending fails
                    }
                }
            } catch (e: Exception) { // Outer catch block for reload or other unexpected errors
                Log.e(TAG, "sendVerificationEmail: Outer catch block error", e)
                 _uiState.update {
                    it.copy(errorMessage = "Failed to send verification email: ${e.message}", isVerificationSent = false)
                }
            }
        } // End viewModelScope.launch
    } // End sendVerificationEmail()

    /**
     * Uploads the selected image to Firebase Storage and triggers the Firestore update.
     * Manages isUploading state and emits events on completion or error.
     * @param imageUri The Uri of the image selected from the gallery.
     */
    fun uploadProfileImageAndUpdateUrl(imageUri: Uri) {
        val userId = userRepository.getCurrentUser()?.uid
        if (userId == null) {
            Log.e(TAG, "uploadProfileImage: User not logged in.")
            viewModelScope.launch { _uploadErrorEvent.emit("User not logged in.") }
            return
        }

        // Set uploading state to true
        _uiState.update { it.copy(isUploading = true, errorMessage = null) }
        Log.d(TAG, "Starting image upload for user: $userId")

        viewModelScope.launch {
            try {
                // First, validate authentication token
                val tokenValid = FirebaseHelper.revalidateAuthToken()
                if (!tokenValid) {
                    Log.e(TAG, "Auth token validation failed during upload")
                    _uiState.update { it.copy(isUploading = false, errorMessage = "Authentication failed. Please sign in again.") }
                    _uploadErrorEvent.emit("Authentication failed. Please sign in again.")
                    return@launch
                }

                // Use consistent path: profile_images/{userId}
                val storageRef = storage.reference.child("$PROFILE_IMAGES_PATH/$userId")

                Log.d(TAG, "Attempting to upload to storage path: ${storageRef.path}")
                Log.d(TAG, "Storage bucket: ${storage.reference.bucket}")
                Log.d(TAG, "Uploading URI: $imageUri")
                Log.d(TAG, "URI scheme: ${imageUri.scheme}")
                Log.d(TAG, "URI authority: ${imageUri.authority}")

                // Upload the file with metadata
                val metadata = com.google.firebase.storage.StorageMetadata.Builder()
                    .setContentType("image/jpeg")
                    .setCustomMetadata("uploadedBy", userId)
                    .setCustomMetadata("uploadedAt", System.currentTimeMillis().toString())
                    .build()

                val uploadTask = storageRef.putFile(imageUri, metadata)
                uploadTask.await()
                Log.d(TAG, "Image uploaded successfully to Storage for user: $userId")

                // Get the download URL
                val downloadUrl = storageRef.downloadUrl.await().toString()
                Log.d(TAG, "Image download URL received: $downloadUrl")

                // Update the temporary photo URL state immediately for preview
                tempPhotoUrl = downloadUrl

                // Trigger the Firestore update process
                finalizeProfilePhotoUpdate(downloadUrl)

            } catch (e: com.google.firebase.storage.StorageException) {
                // Handle specific storage errors
                val errorMessage = when (e.errorCode) {
                    com.google.firebase.storage.StorageException.ERROR_OBJECT_NOT_FOUND -> "Storage location not found"
                    com.google.firebase.storage.StorageException.ERROR_BUCKET_NOT_FOUND -> "Storage bucket not found"
                    com.google.firebase.storage.StorageException.ERROR_PROJECT_NOT_FOUND -> "Firebase project not found"
                    com.google.firebase.storage.StorageException.ERROR_QUOTA_EXCEEDED -> "Storage quota exceeded"
                    com.google.firebase.storage.StorageException.ERROR_NOT_AUTHENTICATED -> "Authentication required for upload"
                    com.google.firebase.storage.StorageException.ERROR_NOT_AUTHORIZED -> "Permission denied for upload"
                    com.google.firebase.storage.StorageException.ERROR_RETRY_LIMIT_EXCEEDED -> "Upload retry limit exceeded"
                    com.google.firebase.storage.StorageException.ERROR_INVALID_CHECKSUM -> "File checksum validation failed"
                    com.google.firebase.storage.StorageException.ERROR_CANCELED -> "Upload was canceled"
                    else -> "Storage error: ${e.message}"
                }
                Log.e(TAG, "Storage upload failed for user: $userId - $errorMessage", e)
                _uiState.update { it.copy(isUploading = false, errorMessage = errorMessage) }
                _uploadErrorEvent.emit(errorMessage)
            } catch (e: Exception) {
                // Handle other upload errors
                Log.e(TAG, "Image upload failed for user: $userId", e)
                val errorMessage = "Upload failed: ${e.message}"
                _uiState.update { it.copy(isUploading = false, errorMessage = errorMessage) }
                _uploadErrorEvent.emit(errorMessage)
            }
        }
    }

    /**
     * Saves the profile to Firestore with the new photo URL.
     * Updates the main UI state and emits completion/error events.
     * Sets isUploading to false upon completion.
     * @param newPhotoUrl The new download URL obtained from Firebase Storage.
     */
    private fun finalizeProfilePhotoUpdate(newPhotoUrl: String) {
         viewModelScope.launch {
            try {
                // Get current user data, apply the new photo URL
                val currentUser = _uiState.value.user
                // Ensure other temp fields are also included if called from photo edit screen context
                val updatedUser = currentUser.copy(
                    displayName = tempDisplayName,
                    photoUrl = newPhotoUrl,
                    bio = tempBio,
                    email = tempEmail
                )

                Log.d(TAG, "Attempting to save profile to Firestore with new photo URL.")
                val success = userRepository.saveUserProfile(updatedUser)

                if (success) {
                    // Update the main UI state with the fully saved user
                    _uiState.update {
                        it.copy(
                            user = updatedUser,
                            isUploading = false, // Stop uploading indicator
                            isLoading = false, // Also ensure loading is false
                            errorMessage = null
                         )
                    }
                    tempPhotoUrl = newPhotoUrl // Ensure tempUrl matches saved state
                    Log.d(TAG, "Firestore profile updated successfully with new photo.")
                    // Emit success event
                    _uploadCompleteEvent.emit(Unit)
                } else {
                    // Handle save failure
                    val errorMsg = "Failed to save profile update to Firestore."
                    Log.e(TAG, errorMsg)
                    _uiState.update {
                        it.copy(
                            isUploading = false, // Stop uploading indicator
                            isLoading = false,
                            errorMessage = errorMsg
                        )
                    }
                     // Emit error event
                    _uploadErrorEvent.emit(errorMsg)
                }
            } catch (e: Exception) {
                // Handle exceptions during Firestore save
                val errorMsg = "Error saving profile update to Firestore: ${e.message}"
                Log.e(TAG, errorMsg, e)
                 _uiState.update {
                    it.copy(
                        isUploading = false, // Stop uploading indicator
                        isLoading = false,
                        errorMessage = errorMsg
                    )
                }
                 // Emit error event
                _uploadErrorEvent.emit(errorMsg)
            }
        }
    }

    /**
     * Clears any error message in the UI state.
     */
    fun clearErrorMessage() {
        _uiState.update { it.copy(errorMessage = null) }
    }

    /**
     * Clears the temporary email field in the ViewModel.
     * Typically called when the user navigates away from the email editing UI.
     */
    fun clearTemporaryEmailField() {
        tempEmail = "" // Reset the temporary email variable
        // Also clear any error message that might be related to email validation
        _uiState.update { it.copy(errorMessage = null) }
        Log.d(TAG, "Temporary email field cleared.")
    }

    /**
     * Saves the changes made in the temporary fields (displayName, bio, photoUrl, email)
     * to the user's profile in both Firebase Auth (for email) and Firestore.
     */
    fun saveProfileChanges() {
        _uiState.update { it.copy(isLoading = true, errorMessage = null) }
        viewModelScope.launch {
            val currentUserAuth = auth.currentUser
            if (currentUserAuth == null) {
                _uiState.update { it.copy(isLoading = false, errorMessage = "User not logged in.") }
                Log.w(TAG, "Save profile changes failed: User not logged in.")
                return@launch
            }

            var emailUpdateSuccess = true // Flag to track if email update succeeded
            val originalEmail = currentUserAuth.email
            val newEmailLower = tempEmail.lowercase() // Normalize new email

            try {
                // 1. Handle Email Update in Firebase Auth first (if changed)
                if (tempEmail.isNotBlank() && tempEmail != originalEmail) {

                    // --- Check for existing pending verification for THIS email by ANOTHER user ---
                    Log.d(TAG, "Checking for existing pending verification for email: $newEmailLower")
                    val existingPendingDocRef = firestore.collection("pendingEmailVerifications").document(newEmailLower)
                    try {
                        val existingPendingDoc = existingPendingDocRef.get().await()
                        if (existingPendingDoc.exists()) {
                            val existingUserId = existingPendingDoc.getString("userId")
                            // Check if the pending record is for a DIFFERENT user
                            if (existingUserId != null && existingUserId != currentUserAuth.uid) {
                                Log.w(TAG, "Email $newEmailLower is already pending verification for another user: $existingUserId.")
                                _uiState.update {
                                    it.copy(isLoading = false, errorMessage = "This email address is already in use or pending verification by another account. Please use a different email.")
                                }
                                return@launch // Stop the process
                            }
                            // If the pending record exists and is for the CURRENT user, we allow the process to continue.
                            // The subsequent .set() operation will attempt to update their existing record.
                            // This will succeed if Firestore rules are adjusted to allow owner-updates.
                            Log.d(TAG, "Email $newEmailLower is already pending for current user or no conflict found. Proceeding.")
                        }
                        // If no existing pending doc, proceed.
                    } catch (e: Exception) {
                        // This catch is for errors during the read of existingPendingDocRef
                        Log.e(TAG, "Error checking existing pending email for $newEmailLower", e)
                        _uiState.update {
                            it.copy(isLoading = false, errorMessage = "Error checking email status. Please try again.")
                        }
                        return@launch
                    }
                    // --- End check ---

                    // --- Create pending verification record BEFORE attempting Auth update ---
                    Log.d(TAG, "Creating pending verification record for email: $newEmailLower")
                    val pendingEmailRef = firestore.collection("pendingEmailVerifications").document(newEmailLower)
                    val pendingData = mapOf(
                        "userId" to currentUserAuth.uid,
                        "email" to newEmailLower,
                        "requestedAt" to FieldValue.serverTimestamp() // Use server timestamp
                    )
                    try {
                        pendingEmailRef.set(pendingData).await() // Write to Firestore
                        Log.d(TAG, "Successfully created pending verification record.")
                    } catch (e: Exception) {
                         Log.e(TAG, "Failed to create pending verification record for $newEmailLower", e)
                         _uiState.update {
                             it.copy(isLoading = false, errorMessage = "Failed to initiate email update process. Please try again.")
                         }
                         return@launch // Stop if we can't even write the pending record
                    }
                    // --- End pending record creation ---

                    Log.d(TAG, "saveProfileChanges: Attempting to update Auth email from $originalEmail to $tempEmail using verifyBeforeUpdateEmail...")
                    try {
                        // Use verifyBeforeUpdateEmail to update and send verification
                        currentUserAuth.verifyBeforeUpdateEmail(tempEmail).await()
                        Log.d(TAG, "saveProfileChanges: SUCCESS - verifyBeforeUpdateEmail completed for $tempEmail")
                        // Update state to reflect verification needed
                        _uiState.update { it.copy(isEmailVerified = false, isVerificationSent = true) }
                        // EMIT EVENT TO SHOW DIALOG
                        _showVerificationSentDialogEvent.emit(Unit)
                    } catch (e: FirebaseAuthUserCollisionException) {
                        emailUpdateSuccess = false // Mark email update as failed
                        val errorMsg = "This email address is already registered to another account. Please use a different email address."
                        Log.w(TAG, "saveProfileChanges: verifyBeforeUpdateEmail FAILED - Email already in use ($tempEmail)", e)
                        _uiState.update {
                            it.copy(isLoading = false, errorMessage = errorMsg)
                        }
                        // *** Attempt to delete the pending record we just created on failure ***
                        try {
                            Log.w(TAG, "Deleting pending record for $newEmailLower due to email collision.")
                            pendingEmailRef.delete().await()
                        } catch (delErr: Exception) {
                             Log.e(TAG, "Failed to delete pending record for $newEmailLower after collision", delErr)
                        }
                        // No return here - let the function finish to potentially show error, Firestore update skipped via emailUpdateSuccess flag
                    } catch (e: FirebaseAuthRecentLoginRequiredException) {
                        Log.w(TAG, "saveProfileChanges: verifyBeforeUpdateEmail FAILED - Requires recent login.", e)
                        _uiState.update {
                            it.copy(isLoading = false, errorMessage = "Updating email requires recent login. Please sign out and sign in.")
                        }
                         // *** Attempt to delete the pending record we just created on failure ***
                         try {
                             Log.w(TAG, "Deleting pending record for $newEmailLower due to re-auth failure.")
                             pendingEmailRef.delete().await()
                         } catch (delErr: Exception) {
                              Log.e(TAG, "Failed to delete pending record for $newEmailLower after re-auth fail", delErr)
                         }
                        return@launch // Stop the saving process
                    } catch (e: Exception) {
                        Log.e(TAG, "saveProfileChanges: verifyBeforeUpdateEmail FAILED for $tempEmail", e)
                        _uiState.update {
                            it.copy(isLoading = false, errorMessage = "Failed to update email: ${e.message}")
                        }
                        // *** Attempt to delete the pending record we just created on failure ***
                         try {
                             Log.w(TAG, "Deleting pending record for $newEmailLower due to general update failure.")
                             pendingEmailRef.delete().await()
                         } catch (delErr: Exception) {
                              Log.e(TAG, "Failed to delete pending record for $newEmailLower after general update fail", delErr)
                         }
                        return@launch // Stop the saving process
                    }
                }

                // 2. Update Profile in Firestore (only if email update succeeded or wasn't needed)
                if (emailUpdateSuccess) {
                    // Get the current user data from state
                    val currentUserState = _uiState.value.user

                    // Create the updated User object
                    val updatedUser = currentUserState.copy(
                        displayName = tempDisplayName,
                        bio = tempBio,
                        photoUrl = tempPhotoUrl, // Keep photoUrl sync'd if it wasn't changed elsewhere
                        email = tempEmail // Use the potentially updated email
                        // Ensure other fields are preserved from currentUserState if needed
                    )

                    Log.d(TAG, "saveProfileChanges: Updating Firestore profile with User object: $updatedUser")
                    // Call the correct repository method with the User object
                    val success = userRepository.saveUserProfile(updatedUser)

                    if (success) {
                        Log.d(TAG, "saveProfileChanges: Firestore profile update successful.")
                        // Update local UI state with the successfully saved User object
                        _uiState.update { currentState ->
                            currentState.copy(
                                user = updatedUser, // Update the user in the state
                                isLoading = false,
                                errorMessage = null // Clear any previous errors
                            )
                        }
                        Log.d(TAG, "Profile changes saved successfully.")
                        isEditMode = false // Exit edit mode after successful save
                    } else {
                         // Handle Firestore save failure
                        val errorMsg = "saveProfileChanges: Failed to save profile changes to Firestore."
                        Log.e(TAG, errorMsg)
                        _uiState.update {
                            it.copy(isLoading = false, errorMessage = errorMsg)
                        }
                    }
                }

            } catch (e: Exception) {
                // Catch unexpected errors during the process
                Log.e(TAG, "saveProfileChanges: Unexpected error saving profile changes", e)
                _uiState.update {
                    it.copy(isLoading = false, errorMessage = "An unexpected error occurred: ${e.message}")
                }
            } finally {
                 // Ensure loading is always set to false
                 if (_uiState.value.isLoading) {
                     _uiState.update { it.copy(isLoading = false) }
                 }
            }
        }
    }

    /**
     * Saves notification preferences to Firestore.
     */
    fun saveNotificationPreferences(
        pollInvitations: Boolean,
        voteNotifications: Boolean,
        pollResults: Boolean,
        commentNotifications: Boolean,
        appUpdates: Boolean,
        notifyOnFriendRequest: Boolean
    ) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, errorMessage = null) }

                // Create a map of notification settings
                val notificationSettings = mapOf(
                    "pollInvitations" to pollInvitations,
                    "voteNotifications" to voteNotifications,
                    "pollResults" to pollResults,
                    "commentNotifications" to commentNotifications,
                    "appUpdates" to appUpdates,
                    "notifyOnFriendRequest" to notifyOnFriendRequest
                )

                // Update the user's notification settings in Firestore
                val success = userRepository.updateNotificationSettings(notificationSettings)

                if (success) {
                    // Fetch updated user profile to reflect changes
                    observeUserProfileUpdates()
                    Log.d(TAG, "Notification preferences saved successfully")
                } else {
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Failed to save notification preferences"
                        )
                    }
                    Log.e(TAG, "Failed to save notification preferences")
                }
            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = "Error saving notification preferences: ${e.message}"
                    )
                }
                Log.e(TAG, "Error saving notification preferences", e)
            }
        }
    }

    /**
     * Saves privacy settings to Firestore.
     */
    fun savePrivacySettings(
        allowTagging: Boolean,
        profileVisibility: String,
        hideOnlineStatus: Boolean,
        publicProfile: Boolean
    ) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, errorMessage = null) }

                // Update the user's privacy settings in Firestore
                val success = userRepository.updatePrivacySettings(
                    allowTagging = allowTagging,
                    profileVisibility = profileVisibility,
                    hideOnlineStatus = hideOnlineStatus,
                    publicProfile = publicProfile
                )

                if (success) {
                    // Fetch updated user profile to reflect changes
                    observeUserProfileUpdates()
                    Log.d(TAG, "Privacy settings saved successfully")
                } else {
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Failed to save privacy settings"
                        )
                    }
                    Log.e(TAG, "Failed to save privacy settings")
                }
            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = "Error saving privacy settings: ${e.message}"
                    )
                }
                Log.e(TAG, "Error saving privacy settings", e)
            }
        }
    }

    /**
     * Signs the user out.
     * @param context Context The activity context to start the login activity
     */
    fun signOut(context: Context? = null) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, errorMessage = null) }

                // Log sign out event
                FirebaseHelper.logEvent("user_signed_out")

                // Cancel user profile observation before signing out
                Log.d(TAG, "Cancelling user profile observation job before sign out.")
                userProfileObservationJob?.cancel()
                userProfileObservationJob = null

                // Reset theme settings to defaults to ensure login screens use default theme
                app.donskey.cantdecide.ui.theme.ThemeManager.resetToDefaults()

                // Sign out from Firebase Auth
                auth.signOut()

                _uiState.update { it.copy(isLoading = false) }
                Log.d(TAG, "User signed out successfully")

                // Navigate to login activity if context is provided
                context?.let {
                    val intent = Intent(it, LoginActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    it.startActivity(intent)
                }
            } catch (e: Exception) {
                val errorMsg = "Error signing out: ${e.message}"
                Log.e(TAG, errorMsg, e)
                // Emit event for main screen dialog
                _showSettingsScreenErrorEvent.emit(errorMsg)
                _uiState.update { it.copy(isLoading = false, errorMessage = null) } // Clear inline error
            }
        }
    }

    /**
     * Saves theme preferences to Firestore.
     */
    fun saveThemePreferences(
        darkMode: Boolean,
        themeColorIndex: Int
    ) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, errorMessage = null) }

                // Update the user's theme preferences in Firestore
                val success = userRepository.updateThemePreferences(
                    darkMode = darkMode,
                    themeColorIndex = themeColorIndex
                )

                if (success) {
                    // Fetch updated user profile to reflect changes
                    observeUserProfileUpdates()
                    Log.d(TAG, "Theme preferences saved successfully")
                } else {
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "Failed to save theme preferences"
                        )
                    }
                    Log.e(TAG, "Failed to save theme preferences")
                }
            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        isLoading = false,
                        errorMessage = "Error saving theme preferences: ${e.message}"
                    )
                }
                Log.e(TAG, "Error saving theme preferences", e)
            }
        }
    }

    /**
     * Deletes the user account, including all associated data.
     * @param context Context The activity context to start the login activity
     */
    fun deleteAccount(context: Context? = null) {
        viewModelScope.launch {
            try {
                _uiState.update { it.copy(isLoading = true, errorMessage = null) }

                // Get current user
                val currentUser = auth.currentUser
                if (currentUser == null) {
                    _uiState.update {
                        it.copy(
                            isLoading = false,
                            errorMessage = "No user is currently signed in"
                        )
                    }
                    return@launch
                }

                // Log deletion attempt
                FirebaseHelper.logEvent("account_deletion_started")

                // Delete user profile image from Storage if it exists
                val userId = currentUser.uid
                if (userId.isNotBlank() && _uiState.value.user.photoUrl.isNotBlank()) {
                    try {
                        // Use the correct path for deletion
                        val storageRef = storage.reference.child("$PROFILE_IMAGES_PATH/$userId")
                        storageRef.delete().await()
                        Log.d(TAG, "Deleted user profile image from storage")
                    } catch (e: Exception) {
                        // Continue with deletion even if image deletion fails
                        Log.w(TAG, "Failed to delete profile image: ${e.message}")
                    }
                }

                // Delete user data from Firestore
                val firestoreDeleted = userRepository.deleteUserAccount()
                if (!firestoreDeleted) {
                    Log.w(TAG, "Failed to delete user data from Firestore")
                }

                // Delete Firebase Auth user
                try {
                    currentUser.delete().await()
                    Log.d(TAG, "Firebase Auth user deleted successfully")

                    // Log successful deletion
                    FirebaseHelper.logEvent("account_deletion_successful")

                    _uiState.update { it.copy(isLoading = false) }

                    // Reset theme settings to defaults to ensure login screens use default theme
                    app.donskey.cantdecide.ui.theme.ThemeManager.resetToDefaults()

                    // Navigate to login activity if context is provided
                    context?.let {
                        val intent = Intent(it, LoginActivity::class.java)
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                        it.startActivity(intent)
                    }
                } catch (e: FirebaseAuthRecentLoginRequiredException) {
                    // Handle case where re-authentication is required
                    val errorMsg = "Please sign out and sign in again to delete your account"
                    Log.e(TAG, "Re-authentication required for account deletion", e)
                    // Emit event for main screen dialog
                    _showSettingsScreenErrorEvent.emit(errorMsg)
                    _uiState.update {
                        it.copy(isLoading = false, errorMessage = null) // Clear inline error
                    }
                } catch (e: Exception) {
                    val errorMsg = "Error deleting account: ${e.message}"
                    Log.e(TAG, "Error deleting Firebase Auth user", e)
                    // Emit event for main screen dialog
                    _showSettingsScreenErrorEvent.emit(errorMsg)
                    _uiState.update {
                        it.copy(isLoading = false, errorMessage = null) // Clear inline error
                    }
                }
            } catch (e: Exception) {
                val errorMsg = "Error during account deletion: ${e.message}"
                Log.e(TAG, errorMsg, e)
                // Emit event for main screen dialog
                _showSettingsScreenErrorEvent.emit(errorMsg)
                _uiState.update {
                    it.copy(isLoading = false, errorMessage = null) // Clear inline error
                }
            }
        }
    }

    override fun onCleared() {
        super.onCleared()
        Log.d(TAG, "SettingsViewModel onCleared called. Cancelling any active jobs.")
        userProfileObservationJob?.cancel()
        userProfileObservationJob = null
    }
}