package app.donskey.cantdecide.data.repository

import android.util.Log
import app.donskey.cantdecide.data.model.PrivateTextPoll
import app.donskey.cantdecide.data.model.PollVote
import app.donskey.cantdecide.data.models.User
import app.donskey.cantdecide.domain.repository.PollsRepository
import app.donskey.cantdecide.ui.poll_details.InvitedFriendVoteStatusViewData
import app.donskey.cantdecide.util.Resource
import com.google.firebase.firestore.FieldValue
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.ExperimentalCoroutinesApi

/**
 * Implementation of [PollsRepository] for interacting with Firestore.
 *
 * @property firestore Instance of [FirebaseFirestore] for database operations.
 */
@Singleton // Typically repositories are singletons
class PollsRepositoryImpl @Inject constructor(
    private val firestore: FirebaseFirestore
) : PollsRepository {

    companion object {
        private const val TAG = "PollsRepositoryImpl"
        private const val POLLS_COLLECTION = "private_polls"
        private const val VOTES_SUBCOLLECTION = "votes"
    }

    // Reference to the Firestore collection where private polls are stored.
    private val pollsCollection = firestore.collection(POLLS_COLLECTION)

    /**
     * Creates a new private text-based poll in Firestore.
     * This method is an alias or alternative to createPrivateTextPoll, ensure consistency.
     * @param poll The [PrivateTextPoll] object to be created.
     * @return A [Resource] indicating success (wrapping pollId) or failure.
     */
    override suspend fun createTextPoll(poll: PrivateTextPoll): Resource<String> {
        return try {
            // If poll.pollId is blank, Firestore will generate a new document ID.
            // Otherwise, it will use the provided pollId.
            val documentReference = pollsCollection.document(poll.pollId.ifBlank { pollsCollection.document().id })
            // Ensure the poll object to be saved has the correct pollId (either new or existing).
            val pollToSave = poll.copy(pollId = documentReference.id)
            // Set the poll data in Firestore. The await() function makes this a suspending call.
            documentReference.set(pollToSave).await()
            // Return a Success Resource with the pollId.
            Resource.Success(documentReference.id)
        } catch (e: Exception) {
            // If any exception occurs, return an Error Resource with the error message.
            Resource.Error(e.message ?: "Failed to create text poll")
        }
    }

    /**
     * Creates a new private text poll in Firestore.
     * Ensures that the poll object is saved with a valid poll ID.
     *
     * @param poll The [PrivateTextPoll] data model instance to be saved.
     * @return A [Resource] wrapper containing the ID of the created poll on success, or an error message on failure.
     */
    override suspend fun createPrivateTextPoll(poll: PrivateTextPoll): Resource<String> {
        return try {
            // Determine the document reference: if pollId is blank, create a new document ID.
            // Otherwise, use the existing pollId.
            val docRef = if (poll.pollId.isBlank()) pollsCollection.document() else pollsCollection.document(poll.pollId)
            // Ensure the pollId field in the poll object matches the document ID.
            val pollToSave = poll.copy(pollId = docRef.id)
            // Asynchronously set the data for the document in Firestore.
            docRef.set(pollToSave).await()
            // On successful creation, return a Resource.Success with the document ID.
            Resource.Success(docRef.id)
        } catch (e: Exception) {
            // If an exception occurs during the Firestore operation, return a Resource.Error.
            Resource.Error(e.message ?: "Failed to create private text poll")
        }
    }

    // Fetches a real-time flow of private text polls created by the specified user.
    // The polls are ordered by creation date in descending order (newest first).
    override fun getPrivateTextPollsCreatedByCurrentUser(userId: String): Flow<List<PrivateTextPoll>> = callbackFlow {
        // Set up a Firestore snapshot listener on the polls collection.
        // This listener will be notified of real-time changes to the data.
        val listenerRegistration = pollsCollection
            // Filter polls where the "creatorId" field matches the provided userId.
            .whereEqualTo("creatorId", userId)
            // Order the results by the "createdAt" field, in descending order (newest polls first).
            .orderBy("createdAt", Query.Direction.DESCENDING)
            // Attach the snapshot listener.
            .addSnapshotListener { snapshot, error ->
                // If there's an error (e.g., permission denied), close the flow with the error.
                if (error != null) {
                    close(error) // Close the flow with the encountered exception.
                    return@addSnapshotListener // Exit the listener callback.
                }
                // If the snapshot is not null (meaning data is available or changes occurred).
                if (snapshot != null) {
                    // Convert the Firestore documents in the snapshot into a list of PrivateTextPoll objects.
                    val polls = snapshot.toObjects(PrivateTextPoll::class.java)
                    // Try to send the list of polls to the flow collector.
                    // .isSuccess is used here to conform to the callbackFlow builder's expectations, ensuring the send attempt is noted.
                    trySend(polls).isSuccess
                }
            }
        // When the flow is cancelled or completed (e.g., ViewModel is cleared), remove the Firestore listener.
        // This is crucial to prevent memory leaks and unnecessary background operations.
        awaitClose { listenerRegistration.remove() }
    }

    // Fetches a real-time flow of private text polls where the specified user is an allowed voter.
    // The polls are sorted by creation date in descending order (newest first) on the client side after retrieval.
    override fun getPrivateTextPollsInvitedToCurrentUser(userId: String): Flow<List<PrivateTextPoll>> = callbackFlow {
        // Set up a Firestore snapshot listener on the polls collection.
        val listenerRegistration = pollsCollection
            // Filter polls where the "allowedVoters" array field contains the provided userId.
            .whereArrayContains("allowedVoters", userId)
            // Attach the snapshot listener.
            // Note: Firestore does not support orderBy on a different field when using array-contains.
            // Sorting will be done client-side after fetching.
            .addSnapshotListener { snapshot, error ->
                // If there's an error, close the flow with the error.
                if (error != null) {
                    close(error) // Close the flow with the encountered exception.
                    return@addSnapshotListener // Exit the listener callback.
                }
                // If the snapshot is not null.
                if (snapshot != null) {
                    // Convert Firestore documents to PrivateTextPoll objects.
                    val polls = snapshot.toObjects(PrivateTextPoll::class.java)
                    // Sort the polls by creation date (newest first) on the client side.
                    // This is done here because Firestore limitations might prevent server-side sorting with 'array-contains'.
                    trySend(polls.sortedByDescending { it.createdAt }).isSuccess
                }
            }
        // When the flow is cancelled or completed, remove the Firestore listener to prevent leaks.
        awaitClose { listenerRegistration.remove() }
    }

    /**
     * Fetches a real-time flow of a specific poll's details from Firestore.
     *
     * @param pollId The ID of the poll to fetch.
     * @return A [Flow] emitting [Resource] wrapping the [PrivateTextPoll] on success, or an error message on failure.
     *         Emits new values whenever the poll document changes.
     */
    override fun getPollDetails(pollId: String): Flow<Resource<PrivateTextPoll?>> = callbackFlow {
        // Reference to the specific poll document.
        val documentRef = pollsCollection.document(pollId)

        // Set up a Firestore snapshot listener on the poll document.
        val listenerRegistration = documentRef.addSnapshotListener { snapshot, error ->
            // If there's an error (e.g., permission denied, document not found initially if not handled gracefully).
            if (error != null) {
                Log.e(TAG, "Firestore listener error for poll $pollId: ${error.message}", error)

                // Create a more detailed error message that includes the exception type
                val errorMessage = when {
                    error.message?.contains("PERMISSION_DENIED") == true ->
                        "PERMISSION_DENIED: ${error.message}"
                    error.message?.contains("Missing or insufficient permissions") == true ->
                        "PERMISSION_DENIED: Missing or insufficient permissions"
                    else -> error.message ?: "Failed to fetch poll details"
                }

                trySend(Resource.Error(errorMessage)).isSuccess
                close(error) // Close the flow with the encountered exception.
                return@addSnapshotListener // Exit the listener callback.
            }

            // If the snapshot is not null and the document exists.
            if (snapshot != null && snapshot.exists()) {
                // Convert the Firestore document to a PrivateTextPoll object.
                val poll = snapshot.toObject(PrivateTextPoll::class.java)
                if (poll != null) {
                    // Try to send the successful resource with the poll data.
                    trySend(Resource.Success(poll)).isSuccess
                } else {
                    // If conversion fails, send an error.
                    trySend(Resource.Error("Failed to convert poll data.")).isSuccess
                }
            } else {
                // If the snapshot is null or the document does not exist (e.g., it was deleted).
                trySend(Resource.Success(null)) // Emit null or a specific state for non-existence
            }
        }
        // When the flow is cancelled or completed, remove the Firestore listener.
        awaitClose { listenerRegistration.remove() }
    }

    /**
     * Fetches a user's specific vote on a given poll from Firestore, if it exists.
     *
     * @param pollId The ID of the poll.
     * @param userId The ID of the user whose vote is to be fetched.
     * @return A [Resource] wrapping the [PollVote] if found (or null if not found), or an error message on failure.
     */
    override suspend fun getUserVote(pollId: String, userId: String): Resource<PollVote?> {
        return try {
            val voteQuerySnapshot = pollsCollection.document(pollId)
                .collection(VOTES_SUBCOLLECTION)
                .whereEqualTo("voterId", userId)
                .limit(1)
                .get()
                .await()

            if (!voteQuerySnapshot.isEmpty) {
                val voteDocument = voteQuerySnapshot.documents.first()
                val vote = voteDocument.toObject(PollVote::class.java)
                Resource.Success(vote) // vote can be null if conversion fails, which is fine for PollVote?
            } else {
                Resource.Success(null) // No vote found for this user on this poll
            }
        } catch (e: Exception) {
            Resource.Error(e.message ?: "Failed to fetch user vote")
        }
    }

    /**
     * Submits a user's vote for a poll to Firestore.
     * The vote is stored in a subcollection named "votes" under the respective poll document.
     *
     * @param vote The [PollVote] object representing the vote to be submitted.
     * @return A [Resource] indicating success or failure of the operation.
     */
    override suspend fun submitVote(vote: PollVote): Resource<Unit> {
        // Ensure pollId and voterId are not empty, as they are crucial.
        if (vote.pollId.isBlank() || vote.voterId.isBlank()) {
            return Resource.Error("Poll ID and Voter ID cannot be empty to submit a vote.")
        }

        return try {
            // Get a reference to the specific poll document.
            val pollDocRef = pollsCollection.document(vote.pollId)

            // The document ID for the vote will be the voter's ID to ensure one vote per user.
            val voteDocRef = pollDocRef.collection(VOTES_SUBCOLLECTION).document(vote.voterId)

            // Prepare the data to be saved for the vote.
            // We use a map to explicitly set the fields.
            // `votedAt` will be set by the server.
            // `voteId` from the input PollVote object is not used as the document ID here.
            val voteData = hashMapOf(
                "pollId" to vote.pollId, // Storing pollId again for potential denormalized queries, though often not needed in a subcollection.
                "voterId" to vote.voterId,
                "optionId" to vote.optionId,
                "votedAt" to FieldValue.serverTimestamp() // Use server timestamp for consistency.
            )

            // Set the vote data in Firestore.
            // Using set() with merge option (or just set() if overwriting is intended and acceptable for re-voting, though our doc ID = voterId prevents true re-vote for different option without delete first).
            // For a simple "cast vote once" scenario, set() is fine.
            voteDocRef.set(voteData).await()

            // Return success.
            Resource.Success(Unit)
        } catch (e: Exception) {
            // If any exception occurs, return an Error Resource with the error message.
            Resource.Error(e.message ?: "Failed to submit vote to Firestore")
        }
    }

    /**
     * Deletes a poll and all its associated votes from Firestore.
     *
     * @param pollId The ID of the poll to delete.
     * @return A [Resource] indicating success ([Resource.Success]) or failure ([Resource.Error]).
     */
    override suspend fun deletePoll(pollId: String): Resource<Unit> {
        // Check if pollId is blank, return error if it is.
        if (pollId.isBlank()) {
            return Resource.Error("Poll ID cannot be blank for deletion.")
        }

        return try {
            // Get a reference to the poll document.
            val pollDocRef = pollsCollection.document(pollId)
            // Get a reference to the votes subcollection.
            val votesCollectionRef = pollDocRef.collection(VOTES_SUBCOLLECTION)

            // 1. Get all votes in the subcollection first.
            // This needs to be done outside the batch if we need to iterate its results to add to the batch.
            val votesSnapshot = votesCollectionRef.get().await() // Perform the suspending call here

            // Firestore batched write to perform multiple operations atomically.
            firestore.runBatch { batch ->
                // Add each vote deletion to the batch
                for (voteDocument in votesSnapshot.documents) {
                    batch.delete(voteDocument.reference)
                }

                // 2. Delete the poll document itself.
                batch.delete(pollDocRef) // Add poll deletion to the batch
            }.await() // Commit the batch and wait for completion (this await() is on the batch itself, which is fine)

            // Return success if the batch operation completes without exceptions.
            Resource.Success(Unit)
        } catch (e: Exception) {
            // If any exception occurs during the batch or querying, return an Error Resource.
            Resource.Error(e.message ?: "Failed to delete poll and its votes")
        }
    }

    // Implementation for fetching the voting status of invited friends.
    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getInvitedFriendsVoteStatus(pollId: String): Flow<Resource<List<InvitedFriendVoteStatusViewData>>> = callbackFlow {
        // Reference to the specific poll document.
        val pollDocRef = firestore.collection("private_polls").document(pollId)

        // Listen for changes to the poll document.
        val pollSubscription = pollDocRef.addSnapshotListener { pollSnapshot, error ->
            if (error != null) {
                trySend(Resource.Error(error.message ?: "Unknown error listening to poll"))
                return@addSnapshotListener
            }
            if (pollSnapshot == null || !pollSnapshot.exists()) {
                trySend(Resource.Error("Poll not found"))
                return@addSnapshotListener
            }

            // Deserialize the poll document into a PrivateTextPoll object.
            val poll = pollSnapshot.toObject(PrivateTextPoll::class.java)
            if (poll == null) {
                trySend(Resource.Error("Failed to parse poll data"))
                return@addSnapshotListener
            }

            // If there are no allowed voters, send an empty list.
            if (poll.allowedVoters.isEmpty()) {
                trySend(Resource.Success(emptyList()))
                return@addSnapshotListener
            }

            // Asynchronously fetch user details and vote information for each allowed voter.
            launch {
                try {
                    // Fetch all user details for the allowed voters in parallel.
                    val users = poll.allowedVoters.mapNotNull { userId ->
                        try {
                            firestore.collection("users").document(userId).get().await().toObject(User::class.java)
                        } catch (e: Exception) {
                            null // If a user is not found or there's an error, skip them.
                        }
                    }

                    // Fetch all votes for the poll in parallel to create a map for quick lookup.
                    val votesSnapshot = firestore.collection("private_polls").document(pollId).collection("votes").get().await()
                    val votesMap = votesSnapshot.documents.mapNotNull { it.toObject(PollVote::class.java) }
                        .associateBy { it.voterId }

                    // Combine user details with their voting status.
                    val viewDataList = users.map { user ->
                        val userVote = votesMap[user.uid]
                        val votedOptionText = if (userVote != null) {
                            poll.options.find { it.optionId == userVote.optionId }?.text
                        } else {
                            null
                        }
                        InvitedFriendVoteStatusViewData(
                            userId = user.uid,
                            username = user.displayName,
                            avatarUrl = user.photoUrl,
                            voteOptionText = votedOptionText,
                            hasVoted = userVote != null
                        )
                    }
                    trySend(Resource.Success(viewDataList))
                } catch (e: Exception) {
                    trySend(Resource.Error(e.message ?: "Error fetching friend vote status details"))
                }
            }
        }
        // Remove the listener when the flow is cancelled.
        awaitClose { pollSubscription.remove() }
    }
}