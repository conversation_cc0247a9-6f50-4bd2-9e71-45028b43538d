2025-05-27 20:04:49.893  9169-9238  Firestore               app.donskey.cantdecide               W  (25.1.1) [Firestore]: Listen for Query(target=Query(private_polls/qg8gSsrY1Z0Z9vRf1mOP order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
2025-05-27 20:04:50.042  9169-9174  skey.cantdecide         app.donskey.cantdecide               I  Background concurrent mark compact GC freed 7981KB AllocSpace bytes, 35(1568KB) LOS objects, 49% free, 11MB/22MB, paused 1.723ms,5.096ms total 57.749ms
2025-05-27 20:04:50.106  9169-9169  AndroidRuntime          app.donskey.cantdecide               E  FATAL EXCEPTION: main
Process: app.donskey.cantdecide, PID: 9169
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@e6ebce9, Dispatchers.Main.immediate]
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
... 26 more
2025-05-27 20:04:50.143  9169-9169  Process                 app.donskey.cantdecide               I  Sending signal. PID: 9169 SIG: 9
---------------------------- PROCESS ENDED (9169) for package app.donskey.cantdecide ----------------------------