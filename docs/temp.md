2025-05-27 14:31:31.291 19892-19955 StorageException app.donskey.cantdecide E StorageException has occurred.
User does not have permission to access this object.
Code: -13021 HttpResult: 403
2025-05-27 14:31:31.292 19892-19955 StorageException app.donskey.cantdecide E The server has terminated the upload session
java.io.IOException: The server has terminated the upload session
at com.google.firebase.storage.UploadTask.serverStateValid(UploadTask.java:359)
at com.google.firebase.storage.UploadTask.shouldContinue(UploadTask.java:325)
at com.google.firebase.storage.UploadTask.run(UploadTask.java:249)
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078)
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65)
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at java.lang.Thread.run(Thread.java:1012)
Caused by: java.io.IOException: { "error": { "code": 403, "message": "Permission denied." }}
at com.google.firebase.storage.network.NetworkRequest.parseResponse(NetworkRequest.java:415)
at com.google.firebase.storage.network.NetworkRequest.parseErrorResponse(NetworkRequest.java:432)
at com.google.firebase.storage.network.NetworkRequest.processResponseStream(NetworkRequest.java:423)
at com.google.firebase.storage.network.NetworkRequest.performRequest(NetworkRequest.java:265)
at com.google.firebase.storage.network.NetworkRequest.performRequest(NetworkRequest.java:282)
at com.google.firebase.storage.UploadTask.send(UploadTask.java:519)
at com.google.firebase.storage.UploadTask.delaySend(UploadTask.java:452)
at com.google.firebase.storage.UploadTask.uploadChunk(UploadTask.java:478)
at com.google.firebase.storage.UploadTask.run(UploadTask.java:248)
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078) 
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47) 
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 14:31:31.292 19892-19955 StorageException app.donskey.cantdecide E StorageException has occurred.
User does not have permission to access this object.
Code: -13021 HttpResult: 403
2025-05-27 14:31:31.293 19892-19966 gralloc4 app.donskey.cantdecide I @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-27 14:31:31.293 19892-19955 StorageException app.donskey.cantdecide E The server has terminated the upload session
java.io.IOException: The server has terminated the upload session
at com.google.firebase.storage.UploadTask.serverStateValid(UploadTask.java:359)
at com.google.firebase.storage.UploadTask.shouldContinue(UploadTask.java:325)
at com.google.firebase.storage.UploadTask.run(UploadTask.java:249)
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078)
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65)
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at java.lang.Thread.run(Thread.java:1012)
Caused by: java.io.IOException: { "error": { "code": 403, "message": "Permission denied." }}
at com.google.firebase.storage.network.NetworkRequest.parseResponse(NetworkRequest.java:415)
at com.google.firebase.storage.network.NetworkRequest.parseErrorResponse(NetworkRequest.java:432)
at com.google.firebase.storage.network.NetworkRequest.processResponseStream(NetworkRequest.java:423)
at com.google.firebase.storage.network.NetworkRequest.performRequest(NetworkRequest.java:265)
at com.google.firebase.storage.network.NetworkRequest.performRequest(NetworkRequest.java:282)
at com.google.firebase.storage.UploadTask.send(UploadTask.java:519)
at com.google.firebase.storage.UploadTask.delaySend(UploadTask.java:452)
at com.google.firebase.storage.UploadTask.uploadChunk(UploadTask.java:478)
at com.google.firebase.storage.UploadTask.run(UploadTask.java:248)
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078) 
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47) 
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 14:31:31.294 19892-19955 StorageException app.donskey.cantdecide E StorageException has occurred.
User does not have permission to access this object.
Code: -13021 HttpResult: 403
2025-05-27 14:31:31.294 19892-19955 StorageException app.donskey.cantdecide E The server has terminated the upload session
java.io.IOException: The server has terminated the upload session
at com.google.firebase.storage.UploadTask.serverStateValid(UploadTask.java:359)
at com.google.firebase.storage.UploadTask.shouldContinue(UploadTask.java:325)
at com.google.firebase.storage.UploadTask.run(UploadTask.java:249)
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078)
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65)
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at java.lang.Thread.run(Thread.java:1012)
Caused by: java.io.IOException: { "error": { "code": 403, "message": "Permission denied." }}
at com.google.firebase.storage.network.NetworkRequest.parseResponse(NetworkRequest.java:415)
at com.google.firebase.storage.network.NetworkRequest.parseErrorResponse(NetworkRequest.java:432)
at com.google.firebase.storage.network.NetworkRequest.processResponseStream(NetworkRequest.java:423)
at com.google.firebase.storage.network.NetworkRequest.performRequest(NetworkRequest.java:265)
at com.google.firebase.storage.network.NetworkRequest.performRequest(NetworkRequest.java:282)
at com.google.firebase.storage.UploadTask.send(UploadTask.java:519)
at com.google.firebase.storage.UploadTask.delaySend(UploadTask.java:452)
at com.google.firebase.storage.UploadTask.uploadChunk(UploadTask.java:478)
at com.google.firebase.storage.UploadTask.run(UploadTask.java:248)
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078) 
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47) 
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 14:31:31.297 19892-19892 SettingsViewModel app.donskey.cantdecide E Storage upload failed for user: Ozr9CG1lm9ZSEfyPV6gZtFLsGTJ2 - Permission denied for upload
com.google.firebase.storage.StorageException: User does not have permission to access this object.
at com.google.firebase.storage.UploadTask.snapStateImpl(UploadTask.java:574)
at com.google.firebase.storage.UploadTask.snapStateImpl(UploadTask.java:57)
at com.google.firebase.storage.StorageTask.snapState(StorageTask.java:344)
at com.google.firebase.storage.StorageTask.getFinalResult(StorageTask.java:454)
at com.google.firebase.storage.StorageTask.getException(StorageTask.java:314)
at kotlinx.coroutines.tasks.TasksKt$awaitImpl$2$1.onComplete(Tasks.kt:142)
                                                                                                    	at com.google.firebase.storage.StorageTask.lambda$new$2$com-google-firebase-storage-StorageTask(StorageTask.java:144)
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda5.raise(D8$$SyntheticClass:0)
at com.google.firebase.storage.TaskListenerImpl.lambda$onInternalStateChanged$2$com-google-firebase-storage-TaskListenerImpl(TaskListenerImpl.java:90)
at com.google.firebase.storage.TaskListenerImpl$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at kotlinx.coroutines.tasks.DirectExecutor.execute(Tasks.kt:164)
at com.google.firebase.storage.internal.SmartHandler.callBack(SmartHandler.java:69)
at com.google.firebase.storage.TaskListenerImpl.onInternalStateChanged(TaskListenerImpl.java:90)
at com.google.firebase.storage.StorageTask.tryChangeState(StorageTask.java:393)
at com.google.firebase.storage.StorageTask.tryChangeState(StorageTask.java:427)
at com.google.firebase.storage.UploadTask.serverStateValid(UploadTask.java:362)
at com.google.firebase.storage.UploadTask.shouldContinue(UploadTask.java:325)
at com.google.firebase.storage.UploadTask.run(UploadTask.java:249)
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078)
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0)
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65)
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47)
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at java.lang.Thread.run(Thread.java:1012)
Caused by: java.io.IOException: The server has terminated the upload session
at com.google.firebase.storage.UploadTask.serverStateValid(UploadTask.java:359)
at com.google.firebase.storage.UploadTask.shouldContinue(UploadTask.java:325) 
at com.google.firebase.storage.UploadTask.run(UploadTask.java:249) 
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078) 
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47) 
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.lang.Thread.run(Thread.java:1012) 
Caused by: java.io.IOException: { "error": { "code": 403, "message": "Permission denied." }}
at com.google.firebase.storage.network.NetworkRequest.parseResponse(NetworkRequest.java:415)
at com.google.firebase.storage.network.NetworkRequest.parseErrorResponse(NetworkRequest.java:432)
at com.google.firebase.storage.network.NetworkRequest.processResponseStream(NetworkRequest.java:423)
at com.google.firebase.storage.network.NetworkRequest.performRequest(NetworkRequest.java:265)
at com.google.firebase.storage.network.NetworkRequest.performRequest(NetworkRequest.java:282)
at com.google.firebase.storage.UploadTask.send(UploadTask.java:519)
at com.google.firebase.storage.UploadTask.delaySend(UploadTask.java:452)
at com.google.firebase.storage.UploadTask.uploadChunk(UploadTask.java:478)
at com.google.firebase.storage.UploadTask.run(UploadTask.java:248)
at com.google.firebase.storage.StorageTask.lambda$getRunnable$7$com-google-firebase-storage-StorageTask(StorageTask.java:1078) 
at com.google.firebase.storage.StorageTask$$ExternalSyntheticLambda1.run(D8$$SyntheticClass:0) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor.lambda$decorate$0$com-google-firebase-concurrent-LimitedConcurrencyExecutor(LimitedConcurrencyExecutor.java:65) 
at com.google.firebase.concurrent.LimitedConcurrencyExecutor$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.concurrent.CustomThreadFactory.lambda$newThread$0$com-google-firebase-concurrent-CustomThreadFactory(CustomThreadFactory.java:47) 
at com.google.firebase.concurrent.CustomThreadFactory$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 14:31:31.300 19892-19966 gralloc4 app.donskey.cantdecide I @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-27 14:31:31.306 19892-19892 Compatibil...geReporter app.donskey.cantdecide D Compat change id reported: 147798919; UID 10321; state: ENABLED
2025-05-27 14:31:31.312 19892-19892 Toast app.donskey.cantdecide I show: caller = app.donskey.cantdecide.ui.settings.SettingsScreenKt$EditPhotoScreen$2$1.emit:1469
2025-05-27 14:31:31.313 19892-19892 Toast app.donskey.cantdecide I show: contextDispId = 0 mCustomDisplayId = -1 focusedDisplayId = 0 isActivityContext = true
2025-05-27 14:31:31.352 19892-19892 RealImageLoader app.donskey.cantdecide I 🚨 Failed - coil.request.NullRequestData - coil.request.NullRequestDataException: The request's data is null.
2025-05-27 14:31:31.394 19892-19966 gralloc4 app.donskey.cantdecide I @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-27 14:31:31.399 19892-19966 gralloc4 app.donskey.cantdecide I @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
