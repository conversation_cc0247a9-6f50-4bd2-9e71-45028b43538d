2025-05-27 19:20:50.887  5023-5023  FriendsRepository       app.donskey.cantdecide               E  Error in friends flow 2: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.888  5023-5023  FriendsRepository       app.donskey.cantdecide               D  Combined friends list size: 1 for user hFNVo8WMAPPlo1xs9RriwunBXDA2
2025-05-27 19:20:50.888  5023-5084  gralloc4                app.donskey.cantdecide               I  @set_metadata: update dataspace from GM (0x00000000 -> 0x08010000)
2025-05-27 19:20:50.888  5023-5023  FriendsViewModel        app.donskey.cantdecide               D  Combine triggered. Friends: 1, Incoming Requests: 0, Outgoing Requests: 0
2025-05-27 19:20:50.888  5023-5023  FriendsViewModel        app.donskey.cantdecide               D  Fetching profiles for IDs: [MDIFs5fCHggRUd34MBUHNZGSehR2]
2025-05-27 19:20:50.888  5023-5023  UserRepository          app.donskey.cantdecide               D  getUserProfilesMap: Attempting to fetch profiles for IDs: [MDIFs5fCHggRUd34MBUHNZGSehR2]
2025-05-27 19:20:50.890  5023-5023  FriendsRepository       app.donskey.cantdecide               E  Error in friends flow 2: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.891  5023-5023  FriendsRepository       app.donskey.cantdecide               D  Combined friends list size: 1 for user hFNVo8WMAPPlo1xs9RriwunBXDA2
2025-05-27 19:20:50.892  5023-5023  FriendsRepository       app.donskey.cantdecide               E  Error in friends flow 1: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.898  5023-5023  FriendsRepository       app.donskey.cantdecide               E  Error in friends flow 1: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.898  5023-5023  FriendsRepository       app.donskey.cantdecide               D  Combined friends list size: 0 for user hFNVo8WMAPPlo1xs9RriwunBXDA2
2025-05-27 19:20:50.899  5023-5023  FriendsRepository       app.donskey.cantdecide               E  Error getting incoming friend requests flow: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.900  5023-5023  FriendsRepository       app.donskey.cantdecide               E  Error getting incoming friend requests flow: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.906  5023-5023  ViewPrivatePollsVM      app.donskey.cantdecide               E  loadPolls: Error collecting combined polls flow: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.908  5023-5023  FriendsRepository       app.donskey.cantdecide               E  Error getting outgoing pending requests flow: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.909  5023-5023  FriendsRepository       app.donskey.cantdecide               E  Error getting outgoing pending requests flow: PERMISSION_DENIED: Missing or insufficient permissions.
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247) 
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642) 
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478) 
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479) 
                                                                                                    	at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60) 
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114) 
                                                                                                    	at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0) 
                                                                                                    	at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67) 
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113) 
                                                                                                    	at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667) 
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654) 
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37) 
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133) 
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307) 
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145) 
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644) 
                                                                                                    	at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-27 19:20:50.965  5023-5101  Firestore               app.donskey.cantdecide               W  (25.1.1) [Firestore]: Listen for Query(target=Query(users where __name__in[users/MDIFs5fCHggRUd34MBUHNZGSehR2] order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=Missing or insufficient permissions., cause=null}
2025-05-27 19:20:51.150  5023-5023  AndroidRuntime          app.donskey.cantdecide               E  FATAL EXCEPTION: main
Process: app.donskey.cantdecide, PID: 5023
com.google.firebase.firestore.FirebaseFirestoreException: PERMISSION_DENIED: Missing or insufficient permissions.
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:113)
at com.google.firebase.firestore.core.EventManager.onError(EventManager.java:247)
at com.google.firebase.firestore.core.SyncEngine.removeAndCleanupTarget(SyncEngine.java:642)
at com.google.firebase.firestore.core.SyncEngine.handleRejectedListen(SyncEngine.java:478)
at com.google.firebase.firestore.core.MemoryComponentProvider$RemoteStoreCallback.handleRejectedListen(MemoryComponentProvider.java:125)
at com.google.firebase.firestore.remote.RemoteStore.processTargetError(RemoteStore.java:596)
at com.google.firebase.firestore.remote.RemoteStore.handleWatchChange(RemoteStore.java:479)
at com.google.firebase.firestore.remote.RemoteStore.access$100(RemoteStore.java:60)
at com.google.firebase.firestore.remote.RemoteStore$1.onWatchChange(RemoteStore.java:188)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:114)
at com.google.firebase.firestore.remote.WatchStream.onNext(WatchStream.java:38)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.lambda$onNext$1$com-google-firebase-firestore-remote-AbstractStream$StreamObserver(AbstractStream.java:126)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)
at com.google.firebase.firestore.remote.AbstractStream$CloseGuardedRunner.run(AbstractStream.java:67)
at com.google.firebase.firestore.remote.AbstractStream$StreamObserver.onNext(AbstractStream.java:113)
at com.google.firebase.firestore.remote.FirestoreChannel$1.onMessage(FirestoreChannel.java:162)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInternal(ClientCallImpl.java:667)
at io.grpc.internal.ClientCallImpl$ClientStreamListenerImpl$1MessagesAvailable.runInContext(ClientCallImpl.java:654)
at io.grpc.internal.ContextRunnable.run(ContextRunnable.java:37)
at io.grpc.internal.SerializingExecutor.run(SerializingExecutor.java:133)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:487)
at java.util.concurrent.FutureTask.run(FutureTask.java:264)
at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:307)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)
at com.google.firebase.firestore.util.AsyncQueue$SynchronizedShutdownAwareExecutor$DelayedStartFactory.run(AsyncQueue.java:235)
at java.lang.Thread.run(Thread.java:1012)
Suppressed: kotlinx.coroutines.internal.DiagnosticCoroutineContextException: [StandaloneCoroutine{Cancelling}@ba3b1, Dispatchers.Main.immediate]
Caused by: io.grpc.StatusException: PERMISSION_DENIED: Missing or insufficient permissions.
at io.grpc.Status.asException(Status.java:541)
at com.google.firebase.firestore.util.Util.exceptionFromStatus(Util.java:111)
... 26 more
2025-05-27 19:20:51.189  5023-5023  Process                 app.donskey.cantdecide               I  Sending signal. PID: 5023 SIG: 9
---------------------------- PROCESS ENDED (5023) for package app.donskey.cantdecide ----------------------------